package com.geeksec.alarm.notification.source;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.alarm.notification.client.AlarmServiceClient;
import com.geeksec.nta.alarm.dto.subscription.NotificationSubscriptionDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.source.SourceFunction;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 订阅配置数据源
 * 在初始化时从告警服务获取订阅配置
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSource implements SourceFunction<NotificationSubscriptionDto> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmServiceClient alarmServiceClient;
    private final ObjectMapper objectMapper;
    
    private volatile boolean running = true;
    
    public SubscriptionConfigSource(AlarmServiceClient alarmServiceClient) {
        this.alarmServiceClient = alarmServiceClient;
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public void run(SourceContext<NotificationSubscriptionDto> ctx) throws Exception {
        log.info("订阅配置数据源启动，初始化时拉取订阅配置");
        
        try {
            // 获取所有有效订阅（仅在初始化时执行一次）
            CompletableFuture<List<NotificationSubscriptionDto>> future = 
                    alarmServiceClient.getAllActiveSubscriptions();
            
            List<NotificationSubscriptionDto> subscriptions = future.get();
            
            if (subscriptions != null && !subscriptions.isEmpty()) {
                log.info("初始化时获取到 {} 个订阅配置", subscriptions.size());
                
                // 发送每个订阅配置
                for (NotificationSubscriptionDto subscription : subscriptions) {
                    if (running) {
                        ctx.collect(subscription);
                    }
                }
            } else {
                log.info("初始化时未获取到任何订阅配置");
            }
            
        } catch (Exception e) {
            log.error("初始化获取订阅配置失败: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，让Flink处理
        }
        
        log.info("订阅配置初始化完成，数据源将保持运行状态等待取消");
        
        // 保持数据源运行状态，等待被取消
        while (running) {
            try {
                Thread.sleep(1000); // 每秒检查一次运行状态
            } catch (InterruptedException e) {
                log.info("订阅配置数据源被中断");
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("订阅配置数据源已停止");
    }
    
    @Override
    public void cancel() {
        log.info("取消订阅配置数据源");
        running = false;
    }
    
    /**
     * 创建默认订阅配置数据源
     */
    public static SubscriptionConfigSource createDefault(String alarmServiceBaseUrl) {
        AlarmServiceClient client = new AlarmServiceClient(alarmServiceBaseUrl);
        return new SubscriptionConfigSource(client);
    }
}
