# Flink 告警通知作业配置
# NTA 3.0 - Alarm Notification Job Configuration

# ==================== 作业基础配置 ====================
job.name=alarm-notification-job
job.parallelism.default=4

# ==================== 检查点配置 ====================
checkpoint.interval=60000
checkpoint.timeout=300000
checkpoint.min-pause-between=5000
checkpoint.max-concurrent=1

# ==================== 并行度配置 ====================
parallelism.kafka-source=4
parallelism.processing=8
parallelism.notification-sink=2
parallelism.notification=2

# ==================== Kafka 配置 ====================
kafka.bootstrap.servers=localhost:9092
kafka.consumer.group.id=alarm-notification-consumer
kafka.starting.offsets=latest
kafka.auto.commit=true
kafka.commit.interval=5000

# ==================== 输入输出主题配置 ====================
input.topic=processed-alarms
subscription.changes.topic=subscription-changes

# ==================== 告警服务配置 ====================
alarm.service.base.url=http://localhost:8080

# ==================== 通知配置 ====================
notification.batch.size=50
notification.linger.ms=1000

# ==================== 邮件配置 ====================
email.smtp.host=localhost
email.smtp.port=587
email.username=
email.password=
email.from.address=<EMAIL>
email.from.name=NTA安全系统
email.tls.enabled=true
email.connection.timeout=10000
email.read.timeout=10000

# ==================== Kafka通知配置 ====================
notification.kafka.bootstrap.servers=localhost:9092
notification.kafka.acks=1
notification.kafka.retries=3
notification.kafka.batch.size=16384
notification.kafka.linger.ms=1
notification.kafka.buffer.memory=33554432
notification.kafka.request.timeout.ms=30000
notification.kafka.connection.timeout.ms=10000

# ==================== 监控配置 ====================
monitoring.enabled=true
monitoring.metrics.interval=30000
monitoring.performance.logging=true
monitoring.detailed.metrics=false