package com.geeksec.nta.alarm.service.core.impl;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.enums.ErrorCode;
import com.geeksec.common.exceptions.BusinessException;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.service.core.AlarmExportService;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 告警导出服务实现类
 * 负责各种告警数据的导出功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmExportServiceImpl implements AlarmExportService {
    
    private final AlarmMapper alarmMapper;
    
    @Value("${alarm.export.pdf.template-path:#{null}}")
    private String pdfTemplatePath;
    
    @Value("${alarm.export.output-dir:#{null}}")
    private String exportOutputDir;
    
    @Override
    public String exportAlarmReport(AlarmListCondition condition) {
        log.info("开始导出告警报告: condition={}", condition);
        
        try {
            // 参数校验
            validateExportCondition(condition);
            
            // 查询告警数据
            List<Map<String, Object>> alarmData = alarmMapper.getAlarmList(condition);
            
            if (CollectionUtils.isEmpty(alarmData)) {
                log.warn("没有找到符合条件的告警数据");
                throw new BusinessException(ErrorCode.NO_DATA_FOUND, "没有找到符合条件的告警数据");
            }
            
            // 生成PDF报告
            String reportFileName = generateReportFileName();
            String reportFilePath = generatePdfReport(alarmData, reportFileName);
            
            log.info("告警报告导出成功: {}", reportFilePath);
            return reportFilePath;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("导出告警报告失败", e);
            throw new BusinessException(ErrorCode.ALARM_EXPORT_ERROR, "导出告警报告失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime) {
        log.info("准备告警会话PCAP下载: userId={}, sessionCount={}, alarmType={}, alarmTime={}", 
                userId, alarmSessionList != null ? alarmSessionList.size() : 0, alarmType, alarmTime);
        
        try {
            // 参数校验
            validatePcapPrepareParams(userId, alarmSessionList, alarmType, alarmTime);
            
            // 查询会话相关的连接信息
            List<Map<String, Object>> sessionConnections = alarmMapper.getSessionConnections(alarmSessionList);
            
            if (CollectionUtils.isEmpty(sessionConnections)) {
                log.warn("未找到告警会话对应的连接信息");
                return ApiResponse.error(ErrorCode.NO_DATA_FOUND, "未找到告警会话对应的连接信息");
            }
            
            // 准备PCAP下载任务
            String taskId = preparePcapDownloadTask(userId, sessionConnections, alarmType, alarmTime);
            
            log.info("PCAP下载任务准备完成: taskId={}", taskId);
            return ApiResponse.success(Map.of(
                    "taskId", taskId,
                    "sessionCount", sessionConnections.size(),
                    "message", "PCAP下载任务已准备完成"
            ));
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("准备告警会话PCAP下载失败", e);
            return ApiResponse.error(ErrorCode.ALARM_PCAP_PREPARE_ERROR, "准备PCAP下载失败: " + e.getMessage());
        }
    }
    
    /**
     * 校验导出条件
     */
    private void validateExportCondition(AlarmListCondition condition) {
        if (condition == null) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "导出条件不能为空");
        }
        
        // 检查导出数量限制
        if (condition.getPageSize() != null && condition.getPageSize() > 10000) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "单次导出数量不能超过10000条");
        }
    }
    
    /**
     * 校验PCAP准备参数
     */
    private void validatePcapPrepareParams(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime) {
        if (userId == null || userId <= 0) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "用户ID不能为空");
        }
        
        if (CollectionUtils.isEmpty(alarmSessionList)) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "告警会话列表不能为空");
        }
        
        if (alarmSessionList.size() > 100) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "单次下载会话数量不能超过100个");
        }
        
        if (alarmType == null || alarmType.trim().isEmpty()) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "告警类型不能为空");
        }
        
        if (alarmTime == null || alarmTime <= 0) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "告警时间不能为空");
        }
    }
    
    /**
     * 生成报告文件名
     */
    private String generateReportFileName() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("alarm_report_%s.pdf", timestamp);
    }
    
    /**
     * 生成PDF报告
     */
    private String generatePdfReport(List<Map<String, Object>> alarmData, String fileName) {
        // TODO: 实现PDF生成逻辑
        // 这里应该使用PDF生成库（如iText、Apache PDFBox等）来生成报告
        log.info("生成PDF报告: fileName={}, dataCount={}", fileName, alarmData.size());
        
        // 临时返回模拟路径
        String filePath = (exportOutputDir != null ? exportOutputDir : "/tmp") + "/" + fileName;
        
        // 实际实现中应该：
        // 1. 使用PDF模板
        // 2. 填充告警数据
        // 3. 生成图表和统计信息
        // 4. 保存到指定路径
        
        return filePath;
    }
    
    /**
     * 准备PCAP下载任务
     */
    private String preparePcapDownloadTask(Integer userId, List<Map<String, Object>> sessionConnections, 
                                         String alarmType, Long alarmTime) {
        // TODO: 实现PCAP下载任务准备逻辑
        // 这里应该：
        // 1. 创建下载任务记录
        // 2. 异步处理PCAP文件准备
        // 3. 返回任务ID供前端查询进度
        
        String taskId = "pcap_task_" + System.currentTimeMillis() + "_" + userId;
        log.info("创建PCAP下载任务: taskId={}, userId={}, sessionCount={}", 
                taskId, userId, sessionConnections.size());
        
        return taskId;
    }
}
