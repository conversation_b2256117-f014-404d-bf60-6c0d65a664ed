package com.geeksec.nta.alarm.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import com.geeksec.nta.alarm.service.AlarmRelationAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 告警关系分析控制器
 * 负责告警关系图谱分析和研判
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@RequestMapping("/alarm/relation")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "告警关系分析", description = "告警关系图谱分析和研判功能")
public class AlarmRelationController {
    
    private final AlarmRelationAnalysisService alarmRelationAnalysisService;
    
    /**
     * 生成告警关系分析图谱
     */
    @PostMapping("/graph")
    @Operation(summary = "生成告警关系图谱", description = "根据告警信息分析攻击链路，构建节点和边的关系网络")
    public ApiResponse<Map<String, Object>> createAlarmRelationGraph(@RequestBody Map<String, Object> alarmMap) {
        log.info("生成告警关系图谱请求");
        
        try {
            Map<String, Object> result = alarmRelationAnalysisService.createAlarmRelationGraph(alarmMap);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("生成告警关系图谱失败", e);
            return ApiResponse.error("生成告警关系图谱失败: " + e.getMessage());
        }
    }
    
    /**
     * 基于告警详情生成关系图谱
     */
    @PostMapping("/graph/by-detail")
    @Operation(summary = "基于告警详情生成关系图谱", description = "通过ES索引和告警ID获取详情后生成关系图谱")
    public ApiResponse<Map<String, Object>> createAlarmRelationGraphByDetail(
            @RequestParam("esIndex") String esIndex,
            @RequestParam("alarmId") String alarmId) {
        log.info("基于告警详情生成关系图谱请求: esIndex={}, alarmId={}", esIndex, alarmId);
        
        try {
            // 这里需要先获取告警详情，然后生成关系图谱
            // 可以注入AlarmDataService来获取详情，或者在Service层处理
            // 为了保持Controller的简洁，建议在Service层处理
            
            // 临时实现：直接调用原有逻辑
            // TODO: 重构为更清晰的实现
            return ApiResponse.error("功能开发中，请使用 /alarm/relation/graph 接口");
            
        } catch (Exception e) {
            log.error("基于告警详情生成关系图谱失败", e);
            return ApiResponse.error("基于告警详情生成关系图谱失败: " + e.getMessage());
        }
    }
    
    /**
     * 基于IP角色生成告警关系扩展分析图谱
     */
    @PostMapping("/graph/by-role")
    @Operation(summary = "基于IP角色生成扩展图谱", description = "通过指定IP地址和角色信息，查询相关告警并进行关系扩展分析")
    public ApiResponse<Map<String, Object>> createAlarmRelationGraphByRole(@RequestBody AlarmRoleJudgeCondition condition) {
        log.info("基于IP角色生成扩展图谱请求: {}", condition);
        
        try {
            Map<String, Object> result = alarmRelationAnalysisService.createAlarmRelationGraphByRole(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("基于IP角色生成扩展图谱失败", e);
            return ApiResponse.error("基于IP角色生成扩展图谱失败: " + e.getMessage());
        }
    }
    
    // TODO: 后续可扩展更多关系分析功能
    
    /**
     * 获取告警关系统计信息
     */
    /*
    @PostMapping("/statistics")
    @Operation(summary = "获取告警关系统计", description = "统计告警关系图谱中的节点和边信息")
    public ApiResponse<Map<String, Object>> getAlarmRelationStatistics(@RequestBody Map<String, Object> condition) {
        log.info("获取告警关系统计请求: {}", condition);
        
        try {
            Map<String, Object> result = alarmRelationAnalysisService.getRelationStatistics(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取告警关系统计失败", e);
            return ApiResponse.error("获取告警关系统计失败: " + e.getMessage());
        }
    }
    */
    
    /**
     * 导出告警关系图谱
     */
    /*
    @PostMapping("/export")
    @Operation(summary = "导出告警关系图谱", description = "将告警关系图谱导出为图片或其他格式")
    public ApiResponse<String> exportAlarmRelationGraph(@RequestBody Map<String, Object> condition) {
        log.info("导出告警关系图谱请求: {}", condition);
        
        try {
            String result = alarmRelationAnalysisService.exportRelationGraph(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("导出告警关系图谱失败", e);
            return ApiResponse.error("导出告警关系图谱失败: " + e.getMessage());
        }
    }
    */
}
