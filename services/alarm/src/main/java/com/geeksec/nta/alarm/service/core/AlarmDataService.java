package com.geeksec.nta.alarm.service.core;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;

import java.util.List;
import java.util.Map;

/**
 * 告警数据管理服务接口
 * 负责告警的基本CRUD操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmDataService {
    
    /**
     * 获取告警列表（分页）
     * 
     * @param condition 查询条件
     * @return 分页结果
     */
    PageResultVo<Map<String, Object>> getAlarmList(AlarmListCondition condition);
    
    /**
     * 获取告警详情
     * 
     * @param esIndex ES索引
     * @param alarmId 告警ID
     * @return 告警详情
     */
    Map<String, Object> getAlarmDetail(String esIndex, String alarmId);
    
    /**
     * 更新告警状态
     * 
     * @param condition 更新条件
     * @return 更新结果
     */
    String updateAlarmStatus(AlarmStatusUpCondition condition);
    
    /**
     * 删除告警文档
     * 
     * @param condition 删除条件 (告警类型 -> 告警ID列表)
     * @return 删除的数量
     * @throws InterruptedException 中断异常
     */
    Long deleteAlarms(Map<Integer, List<String>> condition) throws InterruptedException;
    
    /**
     * 删除所有告警信息
     * 
     * @return 删除结果
     */
    String deleteAllAlarms();
}
