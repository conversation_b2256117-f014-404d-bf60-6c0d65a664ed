package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.domain.model.GraphAnalysisResult;

import java.util.List;
import java.util.Map;

/**
 * 告警图谱构建服务接口
 * 负责将分析结果构建成前端可用的图谱数据结构
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmGraphBuilderService {
    
    /**
     * 构建图谱数据
     * 
     * @param analysisResult 图谱分析结果
     * @return 前端可用的图谱数据
     */
    Map<String, Object> buildGraphData(GraphAnalysisResult analysisResult);
    
    /**
     * 合并多个图谱数据
     * 
     * @param graphDataList 图谱数据列表
     * @return 合并后的图谱数据
     */
    Map<String, Object> mergeGraphData(List<Map<String, Object>> graphDataList);
    
    /**
     * 对图谱数据进行去重和优化
     * 
     * @param graphData 原始图谱数据
     * @return 优化后的图谱数据
     */
    Map<String, Object> optimizeGraphData(Map<String, Object> graphData);
}
