package com.geeksec.nta.alarm.service.impl;

import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;
import com.geeksec.nta.alarm.service.AlarmGraphDataExtractor;
import com.geeksec.nta.alarm.service.AlarmGraphQueryService;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 告警图谱数据提取服务实现类
 * 负责从告警原始数据中提取图谱构建所需的结构化信息
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmGraphDataExtractorImpl implements AlarmGraphDataExtractor {
    
    private final AlarmGraphQueryService queryService;
    
    /**
     * 随机化指纹访问告警类型ID
     */
    private static final Integer RANDOM_FINGER_ALARM_TYPE = 120044;
    
    @Override
    public List<String> extractIpListByRole(Map<String, Object> alarmMap, String role) {
        log.debug("提取IP列表: role={}", role);
        
        List<String> ipList = new ArrayList<>();
        Object mapListObj = alarmMap.get(role);
        
        if (mapListObj instanceof List) {
            List<Map<String, Object>> mapList = (List<Map<String, Object>>) mapListObj;
            for (Map<String, Object> ipMap : mapList) {
                String ipAddr = (String) ipMap.get("ip");
                if (StringUtils.isNotBlank(ipAddr)) {
                    ipList.add(ipAddr);
                }
            }
        }
        
        log.debug("提取到{}个{}角色的IP地址", ipList.size(), role);
        return ipList;
    }
    
    @Override
    public List<AlarmJudgeVertexEntity> extractAttackRoutes(Map<String, Object> alarmMap) {
        log.debug("提取攻击路径信息");
        
        List<AlarmJudgeVertexEntity> routeVertexList = new ArrayList<>();
        Object attackRouteListObj = alarmMap.get("attack_route");
        
        if (attackRouteListObj instanceof List) {
            List<Map<String, Object>> attackRouteList = (List<Map<String, Object>>) attackRouteListObj;
            for (Map<String, Object> routeMap : attackRouteList) {
                String routeType = (String) routeMap.get("type");
                String routeName = (String) routeMap.get("name");
                List<String> tagList = (List<String>) routeMap.get("label");
                
                AlarmJudgeVertexEntity routeVertex = new AlarmJudgeVertexEntity();
                
                // 特殊处理finger类型
                if (StringUtils.equals(routeType, "finger")) {
                    routeType = "SSLFINGER";
                }
                
                routeVertex.setType(routeType != null ? routeType.toUpperCase() : "UNKNOWN");
                routeVertex.setLabel(routeName);
                routeVertex.setVid(routeName);
                routeVertex.setTagList(CollectionUtils.isNotEmpty(tagList) ? tagList : new ArrayList<>());
                routeVertex.setLevel(0);
                routeVertex.setIdentity("route");
                routeVertex.setStatus(StringUtils.EMPTY);
                routeVertex.setNum(StringUtils.EMPTY);
                
                routeVertexList.add(routeVertex);
            }
        }
        
        log.debug("提取到{}个攻击路径节点", routeVertexList.size());
        return routeVertexList;
    }
    
    @Override
    public List<Map<String, Object>> extractConnectionDataList(Map<String, Object> alarmMap, Integer alarmKnowledgeId) {
        log.debug("提取连接数据列表: alarmKnowledgeId={}", alarmKnowledgeId);
        
        List<String> sessionList = (List<String>) alarmMap.get("alarm_session_list");
        if (CollectionUtils.isEmpty(sessionList)) {
            log.debug("会话列表为空，返回空连接数据列表");
            return Collections.emptyList();
        }
        
        List<String> querySessionList = new ArrayList<>(sessionList);
        int limit = sessionList.size();
        
        // 随机化指纹访问类型特殊处理：只查询第一条会话信息
        if (RANDOM_FINGER_ALARM_TYPE.equals(alarmKnowledgeId)) {
            querySessionList = Collections.singletonList(sessionList.get(0));
            limit = 1;
            log.debug("随机化指纹访问类型，只查询第一条会话信息");
        }
        
        List<Map<String, Object>> results = queryService.findConnectionLogsBySessionIds(querySessionList, limit);
        log.debug("提取到{}条连接数据", results.size());
        
        return results;
    }
    
    @Override
    public List<Map<String, Object>> extractTargets(Map<String, Object> alarmMap) {
        log.debug("提取目标信息列表");
        
        Object targetsObj = alarmMap.get("targets");
        if (targetsObj instanceof List) {
            List<Map<String, Object>> targets = (List<Map<String, Object>>) targetsObj;
            log.debug("提取到{}个目标", targets.size());
            return targets;
        }
        
        log.debug("未找到目标信息，返回空列表");
        return Collections.emptyList();
    }
}
