package com.geeksec.nta.alarm.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.service.core.AlarmExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 告警导出控制器
 * 负责告警数据的导出和下载功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@RequestMapping("/alarm/export")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "告警导出下载", description = "告警数据的导出和下载功能")
public class AlarmExportController {
    
    private final AlarmExportService alarmExportService;
    
    /**
     * 导出告警报告PDF
     */
    @PostMapping("/report")
    @Operation(summary = "导出告警报告", description = "根据查询条件导出告警数据为PDF报告")
    public ApiResponse<String> exportAlarmReport(@RequestBody AlarmListCondition condition) {
        log.info("导出告警报告请求: {}", condition);
        
        try {
            String reportPath = alarmExportService.exportAlarmReport(condition);
            return ApiResponse.success(reportPath);
            
        } catch (Exception e) {
            log.error("导出告警报告失败", e);
            return ApiResponse.error("导出告警报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 准备告警会话PCAP下载
     */
    @PostMapping("/pcap/prepare")
    @Operation(summary = "准备PCAP下载", description = "通过告警关联会话ID检索对应会话信息，生成PCAP下载列表")
    public ApiResponse prepareAlarmSessionPcap(
            @RequestParam("userId") Integer userId,
            @RequestParam("alarmSessionList") List<String> alarmSessionList,
            @RequestParam("alarmType") String alarmType,
            @RequestParam("alarmTime") Long alarmTime) {
        log.info("准备PCAP下载请求: userId={}, sessionCount={}, alarmType={}, alarmTime={}", 
                userId, alarmSessionList != null ? alarmSessionList.size() : 0, alarmType, alarmTime);
        
        try {
            return alarmExportService.prepareAlarmSessionPcap(userId, alarmSessionList, alarmType, alarmTime);
            
        } catch (Exception e) {
            log.error("准备PCAP下载失败", e);
            return ApiResponse.error("准备PCAP下载失败: " + e.getMessage());
        }
    }
    
    // TODO: 后续可扩展更多导出功能
    
    /**
     * 导出告警数据为CSV
     */
    /*
    @PostMapping("/csv")
    @Operation(summary = "导出CSV格式", description = "将告警数据导出为CSV格式文件")
    public ApiResponse<String> exportAlarmToCsv(@RequestBody AlarmListCondition condition) {
        log.info("导出CSV格式请求: {}", condition);
        
        try {
            String csvContent = alarmExportService.exportAlarmToCsv(condition);
            return ApiResponse.success(csvContent);
            
        } catch (Exception e) {
            log.error("导出CSV格式失败", e);
            return ApiResponse.error("导出CSV格式失败: " + e.getMessage());
        }
    }
    */
    
    /**
     * 导出告警统计报表
     */
    /*
    @PostMapping("/statistics")
    @Operation(summary = "导出统计报表", description = "导出告警统计分析报表")
    public ApiResponse<String> exportAlarmStatistics(@RequestBody AlarmCommonCondition condition) {
        log.info("导出统计报表请求: {}", condition);
        
        try {
            String reportPath = alarmExportService.exportAlarmStatistics(condition);
            return ApiResponse.success(reportPath);
            
        } catch (Exception e) {
            log.error("导出统计报表失败", e);
            return ApiResponse.error("导出统计报表失败: " + e.getMessage());
        }
    }
    */
    
    /**
     * 获取导出任务状态
     */
    /*
    @GetMapping("/task/status")
    @Operation(summary = "获取导出任务状态", description = "查询导出任务的执行状态和进度")
    public ApiResponse<Map<String, Object>> getExportTaskStatus(@RequestParam("taskId") String taskId) {
        log.info("获取导出任务状态请求: taskId={}", taskId);
        
        try {
            Map<String, Object> taskStatus = alarmExportService.getExportTaskStatus(taskId);
            return ApiResponse.success(taskStatus);
            
        } catch (Exception e) {
            log.error("获取导出任务状态失败: taskId={}", taskId, e);
            return ApiResponse.error("获取导出任务状态失败: " + e.getMessage());
        }
    }
    */
}
