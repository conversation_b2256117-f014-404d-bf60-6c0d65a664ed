package com.geeksec.nta.alarm.service.impl;

import com.geeksec.nta.alarm.domain.model.GraphAnalysisResult;
import com.geeksec.nta.alarm.entity.AlarmJudgeEdgeEntity;
import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;
import com.geeksec.nta.alarm.service.AlarmGraphBuilderService;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 告警图谱构建服务实现类
 * 负责将分析结果构建成前端可用的图谱数据结构
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@Slf4j
public class AlarmGraphBuilderServiceImpl implements AlarmGraphBuilderService {
    
    @Override
    public Map<String, Object> buildGraphData(GraphAnalysisResult analysisResult) {
        log.debug("构建图谱数据");
        
        Map<String, Object> result = new HashMap<>();
        
        if (analysisResult == null || analysisResult.isEmpty()) {
            log.debug("分析结果为空，返回空图谱数据");
            result.put("vertex", new ArrayList<>());
            result.put("edge", new ArrayList<>());
            result.put("label_vertex", new ArrayList<>());
            return result;
        }
        
        // 构建节点数据
        List<AlarmJudgeVertexEntity> vertices = analysisResult.getVertices();
        result.put("vertex", vertices != null ? vertices : new ArrayList<>());
        
        // 构建边数据
        List<AlarmJudgeEdgeEntity> edges = analysisResult.getEdges();
        result.put("edge", edges != null ? edges : new ArrayList<>());
        
        // 构建标签数据
        List<Map<String, Object>> sessionLabels = analysisResult.getSessionLabels();
        result.put("label_vertex", sessionLabels != null ? sessionLabels : new ArrayList<>());
        
        log.debug("图谱数据构建完成: 节点数={}, 边数={}, 标签数={}", 
                 vertices != null ? vertices.size() : 0,
                 edges != null ? edges.size() : 0,
                 sessionLabels != null ? sessionLabels.size() : 0);
        
        return result;
    }
    
    @Override
    public Map<String, Object> mergeGraphData(List<Map<String, Object>> graphDataList) {
        log.debug("合并{}个图谱数据", graphDataList != null ? graphDataList.size() : 0);
        
        if (CollectionUtils.isEmpty(graphDataList)) {
            return buildEmptyGraphData();
        }
        
        List<AlarmJudgeVertexEntity> allVertices = new ArrayList<>();
        List<AlarmJudgeEdgeEntity> allEdges = new ArrayList<>();
        List<Map<String, Object>> allLabels = new ArrayList<>();
        
        // 合并所有图谱数据
        for (Map<String, Object> graphData : graphDataList) {
            if (graphData != null) {
                mergeVertices(graphData, allVertices);
                mergeEdges(graphData, allEdges);
                mergeLabels(graphData, allLabels);
            }
        }
        
        // 构建合并结果
        Map<String, Object> mergedResult = new HashMap<>();
        mergedResult.put("vertex", allVertices);
        mergedResult.put("edge", allEdges);
        mergedResult.put("label_vertex", allLabels);
        
        log.debug("图谱数据合并完成: 节点数={}, 边数={}, 标签数={}", 
                 allVertices.size(), allEdges.size(), allLabels.size());
        
        return optimizeGraphData(mergedResult);
    }
    
    @Override
    public Map<String, Object> optimizeGraphData(Map<String, Object> graphData) {
        log.debug("优化图谱数据");
        
        if (graphData == null || graphData.isEmpty()) {
            return buildEmptyGraphData();
        }
        
        // 去重节点
        List<AlarmJudgeVertexEntity> vertices = deduplicateVertices(
                (List<AlarmJudgeVertexEntity>) graphData.get("vertex"));
        
        // 去重边
        List<AlarmJudgeEdgeEntity> edges = deduplicateEdges(
                (List<AlarmJudgeEdgeEntity>) graphData.get("edge"));
        
        // 去重标签
        List<Map<String, Object>> labels = deduplicateLabels(
                (List<Map<String, Object>>) graphData.get("label_vertex"));
        
        Map<String, Object> optimizedResult = new HashMap<>();
        optimizedResult.put("vertex", vertices);
        optimizedResult.put("edge", edges);
        optimizedResult.put("label_vertex", labels);
        
        log.debug("图谱数据优化完成: 节点数={}, 边数={}, 标签数={}", 
                 vertices.size(), edges.size(), labels.size());
        
        return optimizedResult;
    }
    
    /**
     * 构建空图谱数据
     */
    private Map<String, Object> buildEmptyGraphData() {
        Map<String, Object> emptyData = new HashMap<>();
        emptyData.put("vertex", new ArrayList<>());
        emptyData.put("edge", new ArrayList<>());
        emptyData.put("label_vertex", new ArrayList<>());
        return emptyData;
    }
    
    /**
     * 合并节点数据
     */
    private void mergeVertices(Map<String, Object> graphData, List<AlarmJudgeVertexEntity> allVertices) {
        Object verticesObj = graphData.get("vertex");
        if (verticesObj instanceof List) {
            List<AlarmJudgeVertexEntity> vertices = (List<AlarmJudgeVertexEntity>) verticesObj;
            allVertices.addAll(vertices);
        }
    }
    
    /**
     * 合并边数据
     */
    private void mergeEdges(Map<String, Object> graphData, List<AlarmJudgeEdgeEntity> allEdges) {
        Object edgesObj = graphData.get("edge");
        if (edgesObj instanceof List) {
            List<AlarmJudgeEdgeEntity> edges = (List<AlarmJudgeEdgeEntity>) edgesObj;
            allEdges.addAll(edges);
        }
    }
    
    /**
     * 合并标签数据
     */
    private void mergeLabels(Map<String, Object> graphData, List<Map<String, Object>> allLabels) {
        Object labelsObj = graphData.get("label_vertex");
        if (labelsObj instanceof List) {
            List<Map<String, Object>> labels = (List<Map<String, Object>>) labelsObj;
            allLabels.addAll(labels);
        }
    }
    
    /**
     * 去重节点
     */
    private List<AlarmJudgeVertexEntity> deduplicateVertices(List<AlarmJudgeVertexEntity> vertices) {
        if (CollectionUtils.isEmpty(vertices)) {
            return new ArrayList<>();
        }
        
        Set<String> seenVids = new HashSet<>();
        return vertices.stream()
                .filter(vertex -> vertex != null && vertex.getVid() != null)
                .filter(vertex -> seenVids.add(vertex.getVid()))
                .collect(Collectors.toList());
    }
    
    /**
     * 去重边
     */
    private List<AlarmJudgeEdgeEntity> deduplicateEdges(List<AlarmJudgeEdgeEntity> edges) {
        if (CollectionUtils.isEmpty(edges)) {
            return new ArrayList<>();
        }
        
        Set<String> seenEdges = new HashSet<>();
        return edges.stream()
                .filter(edge -> edge != null && edge.getFrom() != null && edge.getTo() != null)
                .filter(edge -> {
                    String edgeKey = edge.getFrom() + "_" + edge.getTo() + "_" + 
                                   (edge.getLabel() != null ? edge.getLabel() : "");
                    return seenEdges.add(edgeKey);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 去重标签
     */
    private List<Map<String, Object>> deduplicateLabels(List<Map<String, Object>> labels) {
        if (CollectionUtils.isEmpty(labels)) {
            return new ArrayList<>();
        }
        
        Set<String> seenLabels = new HashSet<>();
        return labels.stream()
                .filter(label -> label != null && label.get("id") != null)
                .filter(label -> seenLabels.add((String) label.get("id")))
                .collect(Collectors.toList());
    }
}
