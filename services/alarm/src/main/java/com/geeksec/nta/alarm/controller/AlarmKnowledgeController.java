package com.geeksec.nta.alarm.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.service.core.AlarmKnowledgeService;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 告警知识库控制器
 * 负责告警知识库相关操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@RequestMapping("/alarm/knowledge")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "告警知识库", description = "告警知识库管理功能")
public class AlarmKnowledgeController {
    
    private final AlarmKnowledgeService alarmKnowledgeService;
    
    /**
     * 获取告警知识库列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取知识库列表", description = "获取告警知识库全量列表")
    public ApiResponse<List<KnowledgeAlarmVo>> getKnowledgeAlarmList() {
        log.info("获取告警知识库列表请求");
        
        try {
            List<KnowledgeAlarmVo> result = alarmKnowledgeService.getKnowledgeAlarmList();
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取告警知识库列表失败", e);
            return ApiResponse.error("获取告警知识库列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 初始化告警知识库
     */
    @PostMapping("/init")
    @Operation(summary = "初始化知识库", description = "手动触发告警知识库和采集规则字典的初始化")
    public ApiResponse<String> initKnowledgeType() {
        log.info("手动初始化告警知识库请求");
        
        try {
            alarmKnowledgeService.initKnowledgeType();
            return ApiResponse.success("告警知识库初始化成功");
            
        } catch (Exception e) {
            log.error("初始化告警知识库失败", e);
            return ApiResponse.error("初始化告警知识库失败: " + e.getMessage());
        }
    }
    
    // TODO: 后续可扩展更多知识库管理功能
    
    /**
     * 根据知识库ID获取详情
     */
    /*
    @GetMapping("/detail")
    @Operation(summary = "获取知识库详情", description = "根据知识库ID获取告警知识库详细信息")
    public ApiResponse<KnowledgeAlarmVo> getKnowledgeAlarmById(@RequestParam("knowledgeId") Integer knowledgeId) {
        log.info("获取知识库详情请求: knowledgeId={}", knowledgeId);
        
        try {
            KnowledgeAlarmVo result = alarmKnowledgeService.getKnowledgeAlarmById(knowledgeId);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取知识库详情失败: knowledgeId={}", knowledgeId, e);
            return ApiResponse.error("获取知识库详情失败: " + e.getMessage());
        }
    }
    */
    
    /**
     * 根据告警类型获取相关知识库
     */
    /*
    @GetMapping("/by-type")
    @Operation(summary = "按类型获取知识库", description = "根据告警类型获取相关的知识库列表")
    public ApiResponse<List<KnowledgeAlarmVo>> getKnowledgeAlarmByType(@RequestParam("alarmType") String alarmType) {
        log.info("按类型获取知识库请求: alarmType={}", alarmType);
        
        try {
            List<KnowledgeAlarmVo> result = alarmKnowledgeService.getKnowledgeAlarmByType(alarmType);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("按类型获取知识库失败: alarmType={}", alarmType, e);
            return ApiResponse.error("按类型获取知识库失败: " + e.getMessage());
        }
    }
    */
    
    /**
     * 刷新知识库缓存
     */
    /*
    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新知识库缓存", description = "当知识库配置发生变化时，刷新缓存数据")
    public ApiResponse<String> refreshKnowledgeCache() {
        log.info("刷新知识库缓存请求");
        
        try {
            alarmKnowledgeService.refreshKnowledgeCache();
            return ApiResponse.success("知识库缓存刷新成功");
            
        } catch (Exception e) {
            log.error("刷新知识库缓存失败", e);
            return ApiResponse.error("刷新知识库缓存失败: " + e.getMessage());
        }
    }
    */
}
