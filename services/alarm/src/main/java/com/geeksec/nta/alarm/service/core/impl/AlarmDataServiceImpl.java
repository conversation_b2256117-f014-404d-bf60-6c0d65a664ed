package com.geeksec.nta.alarm.service.core.impl;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.service.core.AlarmDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 告警数据管理服务实现类
 * 负责告警的基本CRUD操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmDataServiceImpl implements AlarmDataService {
    
    private final AlarmMapper alarmMapper;
    
    @Override
    public PageResultVo<Map<String, Object>> getAlarmList(AlarmListCondition condition) {
        log.info("获取告警列表: condition={}", condition);
        
        try {
            // 获取总数
            long total = alarmMapper.countAlarms(condition);
            
            if (total == 0) {
                return PageResultVo.<Map<String, Object>>builder()
                        .records(List.of())
                        .total(0L)
                        .current(condition.getCurrentPage())
                        .size(condition.getPageSize())
                        .build();
            }
            
            // 获取分页数据
            List<Map<String, Object>> records = alarmMapper.getAlarmList(condition);
            
            return PageResultVo.<Map<String, Object>>builder()
                    .records(records)
                    .total(total)
                    .current(condition.getCurrentPage())
                    .size(condition.getPageSize())
                    .build();
                    
        } catch (Exception e) {
            log.error("获取告警列表失败", e);
            throw new RuntimeException("获取告警列表失败", e);
        }
    }
    
    @Override
    public Map<String, Object> getAlarmDetail(String esIndex, String alarmId) {
        log.info("获取告警详情: esIndex={}, alarmId={}", esIndex, alarmId);
        
        try {
            Map<String, Object> alarmDetail = alarmMapper.getAlarmDetail(esIndex, alarmId);
            
            if (alarmDetail == null || alarmDetail.isEmpty()) {
                log.warn("未找到告警详情: esIndex={}, alarmId={}", esIndex, alarmId);
                throw new RuntimeException("告警详情不存在");
            }
            
            return alarmDetail;
            
        } catch (Exception e) {
            log.error("获取告警详情失败: esIndex={}, alarmId={}", esIndex, alarmId, e);
            throw new RuntimeException("获取告警详情失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateAlarmStatus(AlarmStatusUpCondition condition) {
        log.info("更新告警状态: condition={}", condition);
        
        try {
            int updatedCount = alarmMapper.updateAlarmStatus(condition);
            
            if (updatedCount > 0) {
                log.info("成功更新{}条告警状态", updatedCount);
                return "更新告警状态成功";
            } else {
                log.warn("未找到需要更新的告警记录");
                return "未找到需要更新的告警记录";
            }
            
        } catch (Exception e) {
            log.error("更新告警状态失败", e);
            throw new RuntimeException("更新告警状态失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long deleteAlarms(Map<Integer, List<String>> condition) throws InterruptedException {
        log.info("删除告警文档: condition={}", condition);
        
        try {
            // 收集所有需要删除的告警ID
            List<String> allIdsToDelete = condition.values().stream()
                    .flatMap(List::stream)
                    .distinct()
                    .toList();
            
            if (allIdsToDelete.isEmpty()) {
                log.warn("没有需要删除的告警ID");
                return 0L;
            }
            
            // 批量删除相关表数据
            alarmMapper.deleteFromAlarmSourcesByIds(allIdsToDelete);
            alarmMapper.deleteFromAlarmAttackersByIds(allIdsToDelete);
            alarmMapper.deleteFromAlarmReasonsByIds(allIdsToDelete);
            alarmMapper.deleteFromAlarmTargetsByIds(allIdsToDelete);
            alarmMapper.deleteFromAlarmVictimsByIds(allIdsToDelete);
            
            // 最后删除主表数据
            alarmMapper.deleteFromAlarmsByIds(allIdsToDelete);
            
            log.info("成功删除{}条告警记录", allIdsToDelete.size());
            return (long) allIdsToDelete.size();
            
        } catch (Exception e) {
            log.error("删除告警文档失败", e);
            throw new RuntimeException("删除告警文档失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteAllAlarms() {
        log.info("开始删除所有告警数据");
        
        try {
            // 按照子表->主表的顺序清空数据
            alarmMapper.truncateAlarmSources();
            alarmMapper.truncateAlarmAttackers();
            alarmMapper.truncateAlarmVictims();
            alarmMapper.truncateAlarmTargets();
            alarmMapper.truncateAlarmReasons();
            alarmMapper.truncateAlarms();
            
            log.info("所有告警数据删除成功");
            return "删除所有告警成功";
            
        } catch (Exception e) {
            log.error("删除所有告警数据失败", e);
            throw new RuntimeException("删除所有告警数据失败", e);
        }
    }
}
