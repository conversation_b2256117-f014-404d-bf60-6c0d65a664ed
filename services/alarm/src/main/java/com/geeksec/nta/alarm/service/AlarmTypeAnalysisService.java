package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.domain.model.AlarmAnalysisContext;
import com.geeksec.nta.alarm.domain.model.GraphAnalysisResult;

/**
 * 告警类型分析服务接口（内部服务）
 * 负责根据不同告警类型执行相应的分析逻辑
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmTypeAnalysisService {

    /**
     * 根据告警类型分析生成图谱数据
     *
     * @param context 告警分析上下文
     * @return 图谱分析结果
     */
    GraphAnalysisResult analyzeByAlarmType(AlarmAnalysisContext context);

    /**
     * 根据会话ID分析生成图谱数据
     *
     * @param context 告警分析上下文
     * @return 图谱分析结果
     */
    GraphAnalysisResult analyzeBySessionId(AlarmAnalysisContext context);
}