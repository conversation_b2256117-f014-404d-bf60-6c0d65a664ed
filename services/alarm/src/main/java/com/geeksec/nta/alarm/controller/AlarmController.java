package com.geeksec.nta.alarm.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.geeksec.common.controller.BaseController;
import com.geeksec.common.enums.ErrorCode;
import com.geeksec.common.exceptions.BusinessException;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmPcapDownloadCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.service.AlarmRelationAnalysisService;
import com.geeksec.nta.alarm.service.AlarmService;
import com.geeksec.nta.alarm.utils.CheckParamUtil;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警管理控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/alarm")
@RequiredArgsConstructor
@Tag(name = "告警管理", description = "告警管理相关接口")
public class AlarmController extends BaseController {

    final AlarmService alarmService;
    final AlarmRelationAnalysisService alarmRelationAnalysisService;

    /**
     * 获取告警指标信息
     * @param condition
     * @return
     */
    @PostMapping("/target/agg")
    @Operation(summary = "获取告警指标信息")
    public ApiResponse<AlarmTargetAggVo> getAlarmTargetAgg(AlarmCommonCondition condition) {
        try {
            AlarmTargetAggVo result = alarmService.getAlarmTargetAgg(condition);
            return success(result);
        } catch (Exception e) {
            log.error("告警：指标信息异常,e={}", e);
            throw new BusinessException(ErrorCode.ALARM_TARGET_AGGR_ERROR);
        }
    }

    /**
     * 告警页面- 告警攻击链路展示
     * @param condition
     * @return
     */
    @PostMapping("/type/agg")
    @Operation(summary = "告警页面- 告警攻击链路展示")
    public ApiResponse<List<AlarmTypeAggVo.AttackChain>> getModelAlarmAttackChainAggr(AlarmCommonCondition condition) {
        try {
            List<AlarmTypeAggVo.AttackChain> result = alarmService.getModelAlarmAttackChainAggr(condition);
            return success(result);
        } catch (Exception e) {
            log.error("告警：类型聚合异常,e={}", e);
            throw new BusinessException(ErrorCode.ALARM_ATTACK_CHAIN_QUERY_ERROR);
        }
    }

    /**
     * 告警知识库全量查询
     * @return
     */
    @GetMapping("/knowledge")
    @Operation(summary = "告警知识库全量查询")
    public ApiResponse<List<KnowledgeAlarmVo>> getKnowledgeAlarmList() {
        try {
            List<KnowledgeAlarmVo> result = alarmService.getKnowledgeAlarmList();
            return success(result);
        } catch (Exception e) {
            log.error("获取告警知识库失败", e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }


    /**
     * 获取告警列表（分类型）
     * @param condition
     * @return
     */
    @PostMapping("/list")
    @Operation(summary = "获取告警列表（分类型）")
    public ApiResponse<PageResultVo<Map<String, Object>>> getAlarmList(AlarmListCondition condition) {
        String orderField = condition.getOrderField();
        if (!CheckParamUtil.checkPage(condition.getCurrentPage(), condition.getPageSize())) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }
        if (StringUtils.isEmpty(orderField)) {
            condition.setOrderField("time");
        } else {
            if (!("time".equals(orderField) || "alarm_status".equals(orderField)
                    || "attack_chain_name".equals(orderField) || "attack_level".equals(orderField)
                    || "task_id".equals(orderField)
                    || "alarm_knowledge_id".equals(orderField)
                    || "alarm_type".equals(orderField))) {
                throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
            }
        }
        try {
            return ApiResponse.success(alarmService.getAlarmList(condition));
        } catch (Exception e) {
            log.error("告警：分页列表异常,e={}", e);
            throw new BusinessException(ErrorCode.DATA_NOT_FOUND);
        }
    }

    /**
     * 获取告警详情
     * @param params
     * @return
     */
    @PostMapping("/detail")
    @Operation(summary = "告警列表-告警详情")
    public ApiResponse<Map<String, Object>> getAlarmDetail(Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            log.error("告警详情查询参数有误！！");
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }
        try {
            String alarmId = (String) params.get("alarm_id");
            String esIndex = (String) params.get("alarm_index");
            if (StringUtils.isEmpty(alarmId) || StringUtils.isEmpty(esIndex)) {
                throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
            }
            return ApiResponse.success(alarmService.getAlarmDetail2(esIndex, alarmId));
        } catch (Exception e) {
            log.error("告警：告警详情查询失败！", e);
            throw new BusinessException(ErrorCode.DATA_NOT_FOUND);
        }
    }

    /**
     * 获取告警关系分析图谱
     * @param params 告警参数
     * @return 图谱数据
     */
    @PostMapping("/judge")
    @Operation(summary = "告警关系分析-图谱生成")
    public ApiResponse<Map<String, Object>> getAlarmRelationGraph(@RequestBody Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            log.error("告警研判绘图参数为空！！");
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }
        String alarmId = (String) params.get("alarm_id");
        String esIndex = (String) params.get("alarm_index");
        if (StringUtils.isEmpty(alarmId) || StringUtils.isEmpty(esIndex)) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }

        try {
            Map<String, Object> alarmMap = alarmService.getAlarmDetail2(esIndex, alarmId);
            return ApiResponse.success(alarmRelationAnalysisService.createAlarmRelationGraph(alarmMap));
        } catch (Exception e) {
            log.error("告警：告警研判绘图失败！", e);
            throw new BusinessException(ErrorCode.THREAT_DETECTION_FAILED);
        }
    }

    /**
     * 通过IP和对应角色进行告警研判扩展
     * @param condition
     * @return
     */
    @PostMapping("/judge/role")
    @Operation(summary = "通过IP和对应角色进行告警研判扩展")
    public ApiResponse<Map<String, Object>> getAlarmJudgeGraphByRole(AlarmRoleJudgeCondition condition) {
        if (condition == null || condition.getRole() == null || condition.getRole().isEmpty() 
            || StringUtils.isEmpty(condition.getIpAddr()) || condition.getQueryNum() == null 
            || condition.getTimeRange() == null) {
            log.error("告警研判绘图参数为空！！");
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }

        try {
            return ApiResponse.success(alarmRelationAnalysisService.createAlarmRelationGraphByRole(condition));
        } catch (Exception e) {
            log.error("告警：告警研判绘图失败！", e);
            throw new BusinessException(ErrorCode.THREAT_DETECTION_FAILED);
        }
    }

    /**
     * 更新告警文档状态
     * @param condition
     * @return
     */
    @PutMapping("/info")
    @Operation(summary = "更新告警文档状态")
    public ApiResponse<String> updateDoc(AlarmStatusUpCondition condition) {
        try {
            return ApiResponse.success(alarmService.updateDoc(condition));
        } catch (Exception e) {
            log.error("告警：更新文档异常,e={}", e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    /**
     * 删除告警文档
     * @param condition
     * @return
     */
    @DeleteMapping("/list")
    @Operation(summary = "删除告警文档")
    public ApiResponse<Long> deleteDoc(Map<Integer, List<String>> condition) {
        try {
            return ApiResponse.success(alarmService.deleteDoc(condition));
        } catch (Exception e) {
            log.error("告警：删除文档异常,e={}", e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }


    /**
     * 删除所有告警文档
     * @return
     */
    @PostMapping("/deleteAll")
    @Operation(summary = "删除所有告警信息")
    public ApiResponse<String> deleteAllAlarm() {
        try {
            return ApiResponse.success(alarmService.deleteAllAlarm());
        } catch (Exception e) {
            log.error("告警：删除所有告警信息", e);
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    /**
     * 获取告警导出列表
     * @param condition
     * @return
     */
    @PostMapping("/getCsv")
    @Operation(summary = "获取告警导出列表")
    public ApiResponse<String> getCsv(AlarmListCondition condition, HttpServletResponse response) {
        PageResultVo<Map<String, Object>> resultVo = alarmService.getAlarmList(condition);

        List<Map<String, Object>> records = resultVo.getRecords();
        if (CollUtil.isEmpty(records)) {
            return ApiResponse.success("无告警数据可进行导出");
        }
        List<List<String>> rowList = Lists.newArrayListWithCapacity(records.size());
        for (Map<String, Object> record : records) {
            List<String> row = new ArrayList<>();
            row.add(JSON.toJSONString(record));
            rowList.add(row);
        }
        // 写数据
        CsvWriter writer = null;
        String fileName = "Alarm_" + (new Date().getTime()) + ".csv";
        try {
            File csvFile = new File(fileName);
            writer = CsvUtil.getWriter(csvFile, CharsetUtil.CHARSET_UTF_8);
            writer.write(rowList);
            
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            
            // 写入响应流
            try (var fis = new java.io.FileInputStream(csvFile);
                 var os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
        } catch (Exception e) {
            response.setContentType("application/json;charset=utf-8");
            throw new BusinessException(ErrorCode.FILE_OPERATION_FAILED);
        } finally {
            FileUtil.del(fileName);
            if (writer != null) {
                writer.close();
            }
        }
        return ApiResponse.success("操作成功");
    }

    /**
     * 获取告警报告PDF
     * @param condition
     * @param response
     * @param request
     * @return
     */
    @PostMapping("/export/pdf")
    @Operation(summary = "导出告警报告PDF")
    public ApiResponse<String> exportAlarmReport(AlarmListCondition condition, HttpServletResponse response, HttpServletRequest request) {
        String orderField = condition.getOrderField();
        if (!CheckParamUtil.checkPage(condition.getCurrentPage(), condition.getPageSize())) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }
        if (StringUtils.isEmpty(orderField)) {
            condition.setOrderField("time");
        } else {
            if (!("time".equals(orderField) || "alarm_status".equals(orderField)
                    || "attack_chain_name".equals(orderField) || "attack_level".equals(orderField)
                    || "task_id".equals(orderField)
                    || "alarm_knowledge_id".equals(orderField)
                    || "alarm_type".equals(orderField))) {
                throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
            }
        }
        try {
            String filePath = alarmService.exportAlarmReport(condition);
            //String filePath = (String) resultVo.getData();
            File file = new File(filePath);
            if (file.exists() && file.isFile()) {
                // 开始下载
                try {
                    // 设置响应头
                    response.setContentType("application/pdf");
                    response.setHeader("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"");
                    
                    // 写入响应流
                    try (var fis = new java.io.FileInputStream(file);
                         var os = response.getOutputStream()) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = fis.read(buffer)) != -1) {
                            os.write(buffer, 0, bytesRead);
                        }
                        os.flush();
                    }
                    file.delete();
                    return ApiResponse.success("导出告警报告成功");
                } catch (Exception e) {
                    response.setContentType("application/json;charset-uft-8");
                    throw new BusinessException(ErrorCode.FILE_OPERATION_FAILED);
                }
            } else {
                throw new BusinessException(ErrorCode.DATA_NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("告警：导出告警报告失败！", e);
            throw new BusinessException(ErrorCode.FILE_OPERATION_FAILED);
        }
    }

    /**
     * 通过告警关联会话ID检索对应会话信息，生成到告警相关PCAP下载列表
     * @param condition
     * @return
     */
    @PostMapping("/download/prepare/pcap")
    @Operation(summary = "通过告警关联会话ID检索对应会话信息，生成到告警相关PCAP下载列表")
    public ApiResponse pcapPreparByAlarmSession(AlarmPcapDownloadCondition condition) {
        List<String> alarmSessionList = condition.getAlarmSessionList();
        String alarmType = condition.getAlarType();
        Long alarmTime = condition.getAlarmTime();
        Integer userId = condition.getUserId();
        if (CollectionUtil.isEmpty(alarmSessionList) || StringUtils.isEmpty(alarmType) || alarmTime == 0L || ObjectUtils.isEmpty(userId)){
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }

        try {
            return alarmService.prepareAlarmSessionPcap(userId, alarmSessionList, alarmType, alarmTime);
        } catch (Exception e) {
            log.error("告警：下载pcap异常,e={}", e);
            throw new BusinessException(ErrorCode.FILE_OPERATION_FAILED);
        }
    }
}
