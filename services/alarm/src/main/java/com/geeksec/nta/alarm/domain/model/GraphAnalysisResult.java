package com.geeksec.nta.alarm.domain.model;

import com.geeksec.nta.alarm.entity.AlarmJudgeEdgeEntity;
import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 图谱分析结果
 * 封装图谱分析的结果数据
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
public class GraphAnalysisResult {
    
    /**
     * 图谱节点列表
     */
    @Builder.Default
    private List<AlarmJudgeVertexEntity> vertices = new ArrayList<>();
    
    /**
     * 图谱边列表
     */
    @Builder.Default
    private List<AlarmJudgeEdgeEntity> edges = new ArrayList<>();
    
    /**
     * 会话标签列表
     */
    @Builder.Default
    private List<Map<String, Object>> sessionLabels = new ArrayList<>();
    
    /**
     * 创建空的分析结果
     */
    public static GraphAnalysisResult empty() {
        return GraphAnalysisResult.builder().build();
    }
    
    /**
     * 添加节点
     */
    public void addVertex(AlarmJudgeVertexEntity vertex) {
        if (vertex != null) {
            this.vertices.add(vertex);
        }
    }
    
    /**
     * 添加边
     */
    public void addEdge(AlarmJudgeEdgeEntity edge) {
        if (edge != null) {
            this.edges.add(edge);
        }
    }
    
    /**
     * 添加会话标签
     */
    public void addSessionLabel(Map<String, Object> sessionLabel) {
        if (sessionLabel != null) {
            this.sessionLabels.add(sessionLabel);
        }
    }
    
    /**
     * 添加节点列表
     */
    public void addVertices(List<AlarmJudgeVertexEntity> vertices) {
        if (vertices != null) {
            this.vertices.addAll(vertices);
        }
    }
    
    /**
     * 添加边列表
     */
    public void addEdges(List<AlarmJudgeEdgeEntity> edges) {
        if (edges != null) {
            this.edges.addAll(edges);
        }
    }
    
    /**
     * 添加会话标签列表
     */
    public void addSessionLabels(List<Map<String, Object>> sessionLabels) {
        if (sessionLabels != null) {
            this.sessionLabels.addAll(sessionLabels);
        }
    }
    
    /**
     * 合并其他分析结果
     */
    public void merge(GraphAnalysisResult other) {
        if (other != null) {
            addVertices(other.getVertices());
            addEdges(other.getEdges());
            addSessionLabels(other.getSessionLabels());
        }
    }
    
    /**
     * 是否为空结果
     */
    public boolean isEmpty() {
        return (vertices == null || vertices.isEmpty()) &&
               (edges == null || edges.isEmpty()) &&
               (sessionLabels == null || sessionLabels.isEmpty());
    }
}
