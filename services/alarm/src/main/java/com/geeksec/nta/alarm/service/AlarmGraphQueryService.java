package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import com.geeksec.nta.alarm.entity.AnalysisLabelInfoEntity;

import java.util.List;
import java.util.Map;

/**
 * 告警图谱数据查询服务接口
 * 负责从各种数据源查询告警分析所需的原始数据
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmGraphQueryService {
    
    /**
     * 根据角色和IP查询告警数据
     * 
     * @param ipAddr IP地址
     * @param roles 角色列表
     * @param timeRange 时间范围
     * @param limit 查询限制数量
     * @return 告警数据列表
     */
    List<Map<String, Object>> findAlarmsByRoleAndIp(String ipAddr, 
                                                   List<String> roles, 
                                                   AlarmRoleJudgeCondition.TimeRange timeRange, 
                                                   Integer limit);
    
    /**
     * 根据会话ID列表查询连接日志
     * 
     * @param sessionIds 会话ID列表
     * @param limit 查询限制数量
     * @return 连接日志数据列表
     */
    List<Map<String, Object>> findConnectionLogsBySessionIds(List<String> sessionIds, int limit);
    
    /**
     * 根据单个会话ID查询连接日志
     * 
     * @param sessionId 会话ID
     * @return 连接日志数据
     */
    Map<String, Object> findConnectionLogBySessionId(String sessionId);
    
    /**
     * 根据标签ID列表查询标签信息
     * 
     * @param labelIds 标签ID列表
     * @return 标签信息列表
     */
    List<AnalysisLabelInfoEntity> findLabelInfoByIds(List<Integer> labelIds);
}
