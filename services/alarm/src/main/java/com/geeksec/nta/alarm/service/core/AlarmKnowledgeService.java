package com.geeksec.nta.alarm.service.core;

import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;

import java.util.List;

/**
 * 告警知识库服务接口
 * 负责告警知识库相关操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmKnowledgeService {
    
    /**
     * 初始化告警知识库和采集规则字典
     * 系统启动时调用，加载告警规则配置
     */
    void initKnowledgeType();
    
    /**
     * 获取告警知识库全量列表
     * 
     * @return 知识库列表
     */
    List<KnowledgeAlarmVo> getKnowledgeAlarmList();
    
    /**
     * 根据知识库ID获取告警知识库详情
     * 
     * @param knowledgeId 知识库ID
     * @return 知识库详情
     */
    // TODO: 后续可扩展更多知识库功能
    // KnowledgeAlarmVo getKnowledgeAlarmById(Integer knowledgeId);
    
    /**
     * 根据告警类型获取相关知识库
     * 
     * @param alarmType 告警类型
     * @return 相关知识库列表
     */
    // TODO: 后续可扩展更多知识库功能
    // List<KnowledgeAlarmVo> getKnowledgeAlarmByType(String alarmType);
    
    /**
     * 刷新告警知识库缓存
     * 当知识库配置发生变化时调用
     */
    // TODO: 后续可扩展更多知识库功能
    // void refreshKnowledgeCache();
}
