package com.geeksec.nta.alarm.service.handler;

import com.geeksec.nta.alarm.domain.model.AlarmAnalysisContext;
import com.geeksec.nta.alarm.domain.model.GraphBuilder;
import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;
import com.geeksec.nta.alarm.entity.AnalysisLabelInfoEntity;
import com.geeksec.nta.alarm.service.infrastructure.AlarmGraphDataExtractor;
import com.geeksec.nta.alarm.service.infrastructure.AlarmGraphQueryService;
import com.geeksec.nta.alarm.service.AlarmTypeHandler;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 默认告警分析处理器
 * 处理其他类型的告警：挖矿连接(100017)、挖矿病毒(100006)、违规外联(100009)、隐蔽隧道(100010)、随机指纹(120044)
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(Integer.MAX_VALUE) // 最低优先级，作为兜底处理器
public class DefaultAlarmAnalysisHandler implements AlarmTypeHandler {
    
    private static final Set<Integer> SUPPORTED_ALARM_TYPES = Set.of(
            100017, // 尝试挖矿连接
            100006, // 挖矿病毒
            100009, // 违规外联
            100010, // 隐蔽隧道
            120044  // 随机化指纹访问
    );
    
    private final AlarmGraphDataExtractor dataExtractor;
    private final AlarmGraphQueryService queryService;
    
    @Override
    public boolean supports(Integer alarmKnowledgeId) {
        return SUPPORTED_ALARM_TYPES.contains(alarmKnowledgeId);
    }
    
    @Override
    public void analyze(AlarmAnalysisContext context, GraphBuilder builder) {
        Integer alarmKnowledgeId = context.getAlarmKnowledgeId();
        log.debug("开始分析告警类型: {}", alarmKnowledgeId);
        
        try {
            Map<String, Object> alarmMap = context.getAlarmMap();
            
            // 提取基础数据
            List<String> attackerIpList = dataExtractor.extractIpListByRole(alarmMap, "attacker");
            List<String> victimIpList = dataExtractor.extractIpListByRole(alarmMap, "victim");
            List<AlarmJudgeVertexEntity> routeVertexList = dataExtractor.extractAttackRoutes(alarmMap);
            
            // 添加路由节点
            builder.addRouteVertices(routeVertexList);
            
            Set<String> ipSet = new HashSet<>();
            Set<String> processedKeys = new HashSet<>(); // 用于去重（隐蔽隧道类型需要）
            
            // 获取连接数据
            List<Map<String, Object>> connectionDataList = dataExtractor.extractConnectionDataList(
                    alarmMap, context.getAlarmKnowledgeId());
            
            // 随机指纹类型特殊处理：只处理第一条数据
            if (alarmKnowledgeId.equals(120044) && !connectionDataList.isEmpty()) {
                processConnectionData(connectionDataList.get(0), builder, routeVertexList, ipSet, processedKeys, alarmKnowledgeId);
            } else {
                // 处理每个连接数据
                for (Map<String, Object> dataMap : connectionDataList) {
                    processConnectionData(dataMap, builder, routeVertexList, ipSet, processedKeys, alarmKnowledgeId);
                }
            }
            
            // 添加IP节点（按角色分类）
            builder.addIpVerticesByRole(ipSet, attackerIpList, victimIpList);
            
            log.debug("告警类型 {} 分析完成，处理了{}个连接，{}个IP", alarmKnowledgeId, connectionDataList.size(), ipSet.size());
            
        } catch (Exception e) {
            log.error("告警类型 {} 分析失败", context.getAlarmKnowledgeId(), e);
        }
    }
    
    /**
     * 处理单个连接数据
     */
    private void processConnectionData(Map<String, Object> dataMap, 
                                     GraphBuilder builder, 
                                     List<AlarmJudgeVertexEntity> routeVertexList,
                                     Set<String> ipSet,
                                     Set<String> processedKeys,
                                     Integer alarmKnowledgeId) {
        String sip = (String) dataMap.get("sIp");
        String dip = (String) dataMap.get("dIp");
        String key = sip + "_" + dip;
        
        // 隐蔽隧道类型需要去重
        if (alarmKnowledgeId.equals(100010) && processedKeys.contains(key)) {
            return;
        }
        processedKeys.add(key);
        
        ipSet.add(sip);
        ipSet.add(dip);
        
        // 添加连接边
        builder.addConnectionEdge(sip, dip, (String) dataMap.get("AppName"));
        
        // 处理标签
        processLabels(dataMap, builder, key);
        
        // 根据告警类型处理特殊逻辑
        switch (alarmKnowledgeId) {
            case 100017: // 挖矿连接
                processMiningConnection(dataMap, builder, routeVertexList, sip, dip, ipSet);
                break;
            case 100006: // 挖矿病毒
            case 100009: // 违规外联
            case 120044: // 随机指纹
            default:
                // 通用路由连接处理
                processRouteConnections(builder, routeVertexList, sip, dip);
                break;
        }
    }
    
    /**
     * 处理标签信息
     */
    private void processLabels(Map<String, Object> dataMap, GraphBuilder builder, String key) {
        List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new java.util.ArrayList<>());
        if (CollectionUtils.isNotEmpty(labelList)) {
            List<AnalysisLabelInfoEntity> labelsInfo = queryService.findLabelInfoByIds(labelList);
            
            Map<String, Object> labelMap = new HashMap<>();
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            builder.addSessionLabel(labelMap);
            
            // 添加标签边
            builder.addLabelEdge((String) dataMap.get("sIp"), key + "_label", key);
            builder.addLabelEdge(key + "_label", (String) dataMap.get("dIp"), key);
        }
    }
    
    /**
     * 处理挖矿连接特殊逻辑
     */
    private void processMiningConnection(Map<String, Object> dataMap, GraphBuilder builder, 
                                       List<AlarmJudgeVertexEntity> routeVertexList,
                                       String sip, String dip, Set<String> ipSet) {
        // 处理DNS域名IP
        try {
            List<Map<String, Object>> dnsList = (List<Map<String, Object>>) dataMap.get("DNS");
            if (CollectionUtils.isNotEmpty(dnsList)) {
                Map<String, Object> domainIpMap = dnsList.get(0);
                List<String> domainIpList = (List<String>) domainIpMap.get("DomainIp");
                
                if (CollectionUtils.isNotEmpty(domainIpList)) {
                    for (String domainIp : domainIpList) {
                        builder.addLabelEdge(dip, domainIp, "");
                        ipSet.add(domainIp);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("处理挖矿连接DNS信息失败", e);
        }
        
        // 处理域名路由连接
        processRouteConnections(builder, routeVertexList, sip, dip);
    }
    
    /**
     * 处理路由连接
     */
    private void processRouteConnections(GraphBuilder builder, 
                                       List<AlarmJudgeVertexEntity> routeVertexList,
                                       String sip, String dip) {
        if (CollectionUtils.isNotEmpty(routeVertexList)) {
            for (AlarmJudgeVertexEntity routeVertex : routeVertexList) {
                builder.addLabelEdge(sip, routeVertex.getVid(), "");
                builder.addLabelEdge(routeVertex.getVid(), dip, "");
            }
        }
    }
}
