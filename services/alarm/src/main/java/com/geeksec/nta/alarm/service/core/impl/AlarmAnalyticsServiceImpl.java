package com.geeksec.nta.alarm.service.core.impl;

import com.geeksec.common.enums.ErrorCode;
import com.geeksec.common.exceptions.BusinessException;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.service.core.AlarmAnalyticsService;
import com.geeksec.nta.alarm.utils.DorisQueryHelper;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 告警统计分析服务实现类
 * 负责告警的统计和聚合分析
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmAnalyticsServiceImpl implements AlarmAnalyticsService {
    
    private final AlarmMapper alarmMapper;
    
    @Override
    public AlarmTargetAggVo getAlarmTargetAgg(AlarmCommonCondition condition) {
        log.info("获取告警指标统计信息: condition={}", condition);
        
        // 参数校验
        validateCondition(condition);
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 构建查询条件
            Map<String, Object> queryConditions = DorisQueryHelper.buildAlarmQueryConditions(condition);
            
            // 执行聚合查询
            Map<String, Long> aggResults = alarmMapper.getAlarmAggregations(queryConditions);
            
            long endTime = System.currentTimeMillis();
            log.info("告警指标统计查询耗时: {}ms", (endTime - startTime));
            
            // 构建返回结果
            return buildAlarmTargetAggVo(aggResults);
            
        } catch (Exception e) {
            log.error("获取告警指标统计信息失败", e);
            throw new BusinessException(ErrorCode.ALARM_TARGET_AGGR_ERROR);
        }
    }
    
    @Override
    public List<AlarmTypeAggVo.AttackChain> getAttackChainAggr(AlarmCommonCondition condition) {
        log.info("获取告警攻击链路统计: condition={}", condition);
        
        // 参数校验
        validateCondition(condition);
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 构建查询条件
            Map<String, Object> queryConditions = DorisQueryHelper.buildAlarmQueryConditions(condition);
            
            // 执行攻击链聚合查询
            List<AlarmTypeAggVo.AttackChain> attackChains = alarmMapper.getAttackChainAggregations(queryConditions);
            
            long endTime = System.currentTimeMillis();
            log.info("攻击链路统计查询耗时: {}ms", (endTime - startTime));
            
            return attackChains != null ? attackChains : List.of();
            
        } catch (Exception e) {
            log.error("获取告警攻击链路统计失败", e);
            throw new BusinessException(ErrorCode.ALARM_ATTACK_CHAIN_QUERY_ERROR);
        }
    }
    
    /**
     * 参数校验
     */
    private void validateCondition(AlarmCommonCondition condition) {
        if (condition == null) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "查询条件不能为空");
        }
        
        // 时间范围校验
        if (condition.getTimeRange() != null) {
            Long startTime = condition.getTimeRange().getLeft();
            Long endTime = condition.getTimeRange().getRight();
            
            if (startTime != null && endTime != null && startTime > endTime) {
                throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED, "开始时间不能大于结束时间");
            }
        }
    }
    
    /**
     * 构建告警指标聚合结果
     */
    private AlarmTargetAggVo buildAlarmTargetAggVo(Map<String, Long> aggResults) {
        AlarmTargetAggVo resultVo = new AlarmTargetAggVo();
        
        // 从聚合结果中安全地获取计数值
        long lowLevel = aggResults.getOrDefault("low_level_count", 0L);
        long middleLevel = aggResults.getOrDefault("middle_level_count", 0L);
        long highLevel = aggResults.getOrDefault("high_level_count", 0L);
        long totalAlarms = aggResults.getOrDefault("total_alarms", 0L);
        long attackerCount = aggResults.getOrDefault("attacker_count", 0L);
        long victimCount = aggResults.getOrDefault("victim_count", 0L);
        
        // 设置各级别告警数量
        resultVo.setLowLevel(lowLevel);
        resultVo.setMiddleLevel(middleLevel);
        resultVo.setHighLevel(highLevel);
        resultVo.setTotalAlarms(totalAlarms);
        
        // 设置攻击者和受害者数量
        resultVo.setAttackerCount(attackerCount);
        resultVo.setVictimCount(victimCount);
        
        // 计算百分比（如果需要）
        if (totalAlarms > 0) {
            resultVo.setLowLevelPercent((double) lowLevel / totalAlarms * 100);
            resultVo.setMiddleLevelPercent((double) middleLevel / totalAlarms * 100);
            resultVo.setHighLevelPercent((double) highLevel / totalAlarms * 100);
        }
        
        log.debug("构建告警指标聚合结果: {}", resultVo);
        return resultVo;
    }
}
