package com.geeksec.nta.alarm.domain.model;

import com.geeksec.nta.alarm.entity.AlarmJudgeEdgeEntity;
import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 图谱构建器
 * 提供便捷的图谱构建方法
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class GraphBuilder {
    
    private final GraphAnalysisResult result;
    private final Set<String> ipSet = new HashSet<>();
    
    public GraphBuilder() {
        this.result = GraphAnalysisResult.builder().build();
    }
    
    /**
     * 添加IP节点
     */
    public GraphBuilder addIpVertex(String ipAddr, String identity) {
        if (StringUtils.isNotBlank(ipAddr)) {
            ipSet.add(ipAddr);
            
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            ipVertex.setIdentity(identity != null ? identity : "other");
            
            result.addVertex(ipVertex);
        }
        return this;
    }
    
    /**
     * 添加连接边
     */
    public GraphBuilder addConnectionEdge(String from, String to, String label) {
        if (StringUtils.isNotBlank(from) && StringUtils.isNotBlank(to)) {
            String key = from + "_" + to;
            
            AlarmJudgeEdgeEntity edge = new AlarmJudgeEdgeEntity();
            edge.setFrom(from);
            edge.setTo(to);
            edge.setLabel(label != null ? label : StringUtils.EMPTY);
            edge.setRankId(key);
            
            result.addEdge(edge);
        }
        return this;
    }
    
    /**
     * 添加标签边
     */
    public GraphBuilder addLabelEdge(String from, String to, String rankId) {
        if (StringUtils.isNotBlank(from) && StringUtils.isNotBlank(to)) {
            AlarmJudgeEdgeEntity edge = new AlarmJudgeEdgeEntity();
            edge.setFrom(from);
            edge.setTo(to);
            edge.setLabel(StringUtils.EMPTY);
            edge.setRankId(rankId != null ? rankId : StringUtils.EMPTY);
            
            result.addEdge(edge);
        }
        return this;
    }
    
    /**
     * 添加路由节点列表
     */
    public GraphBuilder addRouteVertices(List<AlarmJudgeVertexEntity> routeVertices) {
        if (routeVertices != null) {
            result.addVertices(routeVertices);
        }
        return this;
    }
    
    /**
     * 添加会话标签
     */
    public GraphBuilder addSessionLabel(Map<String, Object> sessionLabel) {
        if (sessionLabel != null) {
            result.addSessionLabel(sessionLabel);
        }
        return this;
    }
    
    /**
     * 批量添加IP节点（根据角色分类）
     */
    public GraphBuilder addIpVerticesByRole(Set<String> ipAddresses, 
                                           List<String> attackerIps, 
                                           List<String> victimIps) {
        if (ipAddresses != null) {
            for (String ipAddr : ipAddresses) {
                String identity = "other";
                if (attackerIps != null && attackerIps.contains(ipAddr)) {
                    identity = "attacker";
                } else if (victimIps != null && victimIps.contains(ipAddr)) {
                    identity = "victim";
                }
                addIpVertex(ipAddr, identity);
            }
        }
        return this;
    }
    
    /**
     * 获取构建结果
     */
    public GraphAnalysisResult build() {
        return result;
    }
    
    /**
     * 获取已收集的IP集合
     */
    public Set<String> getCollectedIps() {
        return new HashSet<>(ipSet);
    }
}
