package com.geeksec.nta.alarm.service.core;

import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;

import java.util.List;

/**
 * 告警统计分析服务接口
 * 负责告警的统计和聚合分析
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmAnalyticsService {
    
    /**
     * 获取告警指标统计信息
     * 包括各级别告警数量、攻击者数量、受害者数量等
     * 
     * @param condition 查询条件
     * @return 告警指标统计结果
     */
    AlarmTargetAggVo getAlarmTargetAgg(AlarmCommonCondition condition);
    
    /**
     * 获取告警攻击链路统计
     * 用于告警页面的攻击链路展示
     * 
     * @param condition 查询条件
     * @return 攻击链路统计结果
     */
    List<AlarmTypeAggVo.AttackChain> getAttackChainAggr(AlarmCommonCondition condition);
    
    /**
     * 获取告警趋势分析
     * 按时间维度统计告警数量变化趋势
     * 
     * @param condition 查询条件
     * @return 趋势分析结果
     */
    // TODO: 后续可扩展更多分析功能
    // List<AlarmTrendVo> getAlarmTrend(AlarmCommonCondition condition);
    
    /**
     * 获取告警热点分析
     * 统计最频繁的攻击者、受害者、攻击类型等
     * 
     * @param condition 查询条件
     * @return 热点分析结果
     */
    // TODO: 后续可扩展更多分析功能
    // AlarmHotspotVo getAlarmHotspot(AlarmCommonCondition condition);
}
