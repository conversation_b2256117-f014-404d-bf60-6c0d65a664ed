package com.geeksec.nta.alarm.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.service.core.AlarmDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 告警数据管理控制器
 * 负责告警的基本CRUD操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@RequestMapping("/alarm/data")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "告警数据管理", description = "告警数据的基本CRUD操作")
public class AlarmDataController {
    
    private final AlarmDataService alarmDataService;
    
    /**
     * 获取告警列表（分页）
     */
    @PostMapping("/list")
    @Operation(summary = "获取告警列表", description = "根据条件分页查询告警列表")
    public ApiResponse<PageResultVo<Map<String, Object>>> getAlarmList(@RequestBody AlarmListCondition condition) {
        log.info("获取告警列表请求: {}", condition);
        
        try {
            PageResultVo<Map<String, Object>> result = alarmDataService.getAlarmList(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取告警列表失败", e);
            return ApiResponse.error("获取告警列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取告警详情
     */
    @GetMapping("/detail")
    @Operation(summary = "获取告警详情", description = "根据ES索引和告警ID获取告警详细信息")
    public ApiResponse<Map<String, Object>> getAlarmDetail(
            @RequestParam("esIndex") String esIndex,
            @RequestParam("alarmId") String alarmId) {
        log.info("获取告警详情请求: esIndex={}, alarmId={}", esIndex, alarmId);
        
        try {
            Map<String, Object> alarmDetail = alarmDataService.getAlarmDetail(esIndex, alarmId);
            return ApiResponse.success(alarmDetail);
            
        } catch (Exception e) {
            log.error("获取告警详情失败: esIndex={}, alarmId={}", esIndex, alarmId, e);
            return ApiResponse.error("获取告警详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新告警状态
     */
    @PutMapping("/status")
    @Operation(summary = "更新告警状态", description = "批量更新告警的处理状态")
    public ApiResponse<String> updateAlarmStatus(@RequestBody AlarmStatusUpCondition condition) {
        log.info("更新告警状态请求: {}", condition);
        
        try {
            String result = alarmDataService.updateAlarmStatus(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("更新告警状态失败", e);
            return ApiResponse.error("更新告警状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除告警文档
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除告警", description = "根据告警类型和ID列表批量删除告警")
    public ApiResponse<Long> deleteAlarms(@RequestBody Map<Integer, List<String>> condition) {
        log.info("批量删除告警请求: {}", condition);
        
        try {
            Long deletedCount = alarmDataService.deleteAlarms(condition);
            return ApiResponse.success(deletedCount);
            
        } catch (Exception e) {
            log.error("批量删除告警失败", e);
            return ApiResponse.error("批量删除告警失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除所有告警
     */
    @DeleteMapping("/all")
    @Operation(summary = "删除所有告警", description = "清空所有告警数据，谨慎操作")
    public ApiResponse<String> deleteAllAlarms() {
        log.warn("删除所有告警数据请求");
        
        try {
            String result = alarmDataService.deleteAllAlarms();
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("删除所有告警失败", e);
            return ApiResponse.error("删除所有告警失败: " + e.getMessage());
        }
    }
}
