package com.geeksec.nta.alarm.service.impl;

import com.geeksec.nta.alarm.entity.AlarmSuppression;
import com.geeksec.nta.alarm.mapper.AlarmSuppressionMapper;
import com.geeksec.nta.alarm.service.AlarmSuppressionEventPublisher;
import com.geeksec.nta.alarm.service.AlarmSuppressionService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * 告警抑制规则服务实现
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmSuppressionServiceImpl implements AlarmSuppressionService {

    private final AlarmSuppressionMapper alarmSuppressionMapper;
    private final AlarmSuppressionEventPublisher eventPublisher;
    
    /**
     * 抑制规则缓存，使用ConcurrentHashMap保证线程安全
     * Key: victim:attacker:label
     * Value: AlarmSuppression对象
     */
    private final Map<String, AlarmSuppression> suppressionCache = new ConcurrentHashMap<>();
    
    /**
     * 缓存刷新间隔（毫秒）
     */
    private static final long CACHE_REFRESH_INTERVAL = 5 * 60 * 1000L; // 5分钟
    

    
    // ==================== 告警白名单检查接口实现 ====================
    
    @Override
    public boolean shouldSuppressAlarm(String victim, String attacker, String label) {
        if (!StringUtils.hasText(victim) || !StringUtils.hasText(attacker) || !StringUtils.hasText(label)) {
            return false;
        }

        try {
            return alarmSuppressionMapper.existsByVictimAndAttackerAndLabel(victim, attacker, label);
        } catch (Exception e) {
            log.error("检查告警抑制规则失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
            return false;
        }
    }
    
    @Override
    public boolean shouldSuppressAttackChain(String victim, String attacker) {
        if (!StringUtils.hasText(victim) || !StringUtils.hasText(attacker)) {
            return false;
        }

        try {
            // 检查是否有任何标签的抑制规则匹配
            List<AlarmSuppression> victimSuppressions = alarmSuppressionMapper.selectByVictim(victim);
            return victimSuppressions.stream()
                    .anyMatch(suppression -> attacker.equals(suppression.getAttacker()));
        } catch (Exception e) {
            log.error("检查攻击链抑制规则失败: victim={}, attacker={}", victim, attacker, e);
            return false;
        }
    }
    
    @Override
    public List<Boolean> batchCheckAlarms(List<AlarmCheckItem> alarmList) {
        List<Boolean> results = new ArrayList<>();
        
        if (alarmList == null || alarmList.isEmpty()) {
            return results;
        }
        
        for (AlarmCheckItem alarm : alarmList) {
            boolean shouldSuppress = shouldSuppressAlarm(alarm.getVictim(), alarm.getAttacker(), alarm.getLabel());
            results.add(shouldSuppress);
        }
        
        return results;
    }
    
    // ==================== 告警白名单管理接口实现 ====================
    
    @Override
    @Transactional
    public boolean addSuppressionRule(String victim, String attacker, String label) {
        if (!StringUtils.hasText(victim) || !StringUtils.hasText(attacker) || !StringUtils.hasText(label)) {
            log.warn("添加告警抑制规则失败：参数不能为空");
            return false;
        }
        
        try {
            // 检查是否已存在
            if (alarmSuppressionMapper.existsByVictimAndAttackerAndLabel(victim, attacker, label)) {
                log.info("告警抑制规则已存在: victim={}, attacker={}, label={}", victim, attacker, label);
                return true;
            }
            
            AlarmSuppression suppression = new AlarmSuppression();
            suppression.setVictim(victim);
            suppression.setAttacker(attacker);
            suppression.setLabel(label);
            
            int result = alarmSuppressionMapper.insert(suppression);
            if (result > 0) {
                // 发布Kafka事件
                eventPublisher.publishSuppressionAdded(victim, attacker, label);

                log.info("成功添加告警抑制规则: victim={}, attacker={}, label={}", victim, attacker, label);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("添加告警抑制规则失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean removeSuppressionRule(String victim, String attacker, String label) {
        if (!StringUtils.hasText(victim) || !StringUtils.hasText(attacker) || !StringUtils.hasText(label)) {
            log.warn("移除告警抑制规则失败：参数不能为空");
            return false;
        }
        
        try {
            int result = alarmSuppressionMapper.deleteByVictimAndAttackerAndLabel(victim, attacker, label);
            if (result > 0) {
                // 发布Kafka事件
                eventPublisher.publishSuppressionRemoved(victim, attacker, label);

                log.info("成功移除告警抑制规则: victim={}, attacker={}, label={}", victim, attacker, label);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("移除告警抑制规则失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public int batchAddSuppressionRules(List<AlarmCheckItem> alarmList) {
        if (alarmList == null || alarmList.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        for (AlarmCheckItem alarm : alarmList) {
            if (addSuppressionRule(alarm.getVictim(), alarm.getAttacker(), alarm.getLabel())) {
                successCount++;
            }
        }
        
        log.info("批量添加告警抑制规则完成: 总数={}, 成功={}", alarmList.size(), successCount);
        return successCount;
    }
    
    @Override
    @Transactional
    public int removeSuppressionRulesByCondition(String victim, String attacker, String label) {
        try {
            int result = alarmSuppressionMapper.deleteByCondition(victim, attacker, label);
            
            if (result > 0) {
                // 发布事件
                eventPublisher.publishBatchSuppressionRemoved(victim, attacker, label, result);

                log.info("根据条件删除告警抑制规则成功: victim={}, attacker={}, label={}, 删除数量={}",
                        victim, attacker, label, result);
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据条件删除告警抑制规则失败: victim={}, attacker={}, label={}", 
                    victim, attacker, label, e);
            return 0;
        }
    }
    
    @Override
    public List<AlarmSuppression> getSuppressionRules() {
        try {
            return alarmSuppressionMapper.selectAll();
        } catch (Exception e) {
            log.error("获取告警抑制规则失败", e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public List<AlarmSuppression> getSuppressionRulesByCondition(String victim, String attacker, String label) {
        try {
            return alarmSuppressionMapper.selectByCondition(victim, attacker, label);
        } catch (Exception e) {
            log.error("根据条件获取告警抑制规则失败: victim={}, attacker={}, label={}", 
                    victim, attacker, label, e);
            return Collections.emptyList();
        }
    }
    
    // ==================== 缓存和统计接口实现 ====================
    
    @Override
    public void refreshCache() {
        try {
            log.info("开始刷新告警抑制规则缓存");
            
            // 清空现有缓存
            suppressionCache.clear();
            
            // 从数据库加载所有抑制规则
            List<AlarmSuppression> allSuppressions = alarmSuppressionMapper.selectAll();
            
            // 重建缓存
            for (AlarmSuppression suppression : allSuppressions) {
                String cacheKey = buildCacheKey(suppression.getVictim(), 
                                              suppression.getAttacker(), 
                                              suppression.getLabel());
                suppressionCache.put(cacheKey, suppression);
            }
            
            // 更新缓存时间
            cacheLastUpdateTime = System.currentTimeMillis();
            
            log.info("告警抑制规则缓存刷新完成，共加载{}条记录", allSuppressions.size());
        } catch (Exception e) {
            log.error("刷新告警抑制规则缓存失败", e);
        }
    }
    

    

}
