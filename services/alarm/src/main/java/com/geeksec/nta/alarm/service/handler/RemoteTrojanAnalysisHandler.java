package com.geeksec.nta.alarm.service.handler;

import com.geeksec.nta.alarm.domain.model.AlarmAnalysisContext;
import com.geeksec.nta.alarm.domain.model.GraphBuilder;
import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;
import com.geeksec.nta.alarm.entity.AnalysisLabelInfoEntity;
import com.geeksec.nta.alarm.service.AlarmGraphDataExtractor;
import com.geeksec.nta.alarm.service.AlarmGraphQueryService;
import com.geeksec.nta.alarm.service.AlarmTypeHandler;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 远控木马告警分析处理器
 * 处理告警知识库ID为100005的远控木马告警
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RemoteTrojanAnalysisHandler implements AlarmTypeHandler {
    
    private static final Integer REMOTE_TROJAN_ALARM_TYPE = 100005;
    
    private final AlarmGraphDataExtractor dataExtractor;
    private final AlarmGraphQueryService queryService;
    
    @Override
    public boolean supports(Integer alarmKnowledgeId) {
        return Objects.equals(alarmKnowledgeId, REMOTE_TROJAN_ALARM_TYPE);
    }
    
    @Override
    public void analyze(AlarmAnalysisContext context, GraphBuilder builder) {
        log.debug("开始分析远控木马告警");
        
        try {
            Map<String, Object> alarmMap = context.getAlarmMap();
            
            // 提取基础数据
            List<String> attackerIpList = dataExtractor.extractIpListByRole(alarmMap, "attacker");
            List<String> victimIpList = dataExtractor.extractIpListByRole(alarmMap, "victim");
            List<AlarmJudgeVertexEntity> routeVertexList = dataExtractor.extractAttackRoutes(alarmMap);
            
            // 添加路由节点
            builder.addRouteVertices(routeVertexList);
            
            Set<String> ipSet = new HashSet<>();
            
            // 获取连接数据
            List<Map<String, Object>> connectionDataList = dataExtractor.extractConnectionDataList(
                    alarmMap, context.getAlarmKnowledgeId());
            
            // 处理每个连接数据
            for (Map<String, Object> dataMap : connectionDataList) {
                processConnectionData(dataMap, builder, routeVertexList, ipSet);
            }
            
            // 添加IP节点（按角色分类）
            builder.addIpVerticesByRole(ipSet, attackerIpList, victimIpList);
            
            log.debug("远控木马告警分析完成，处理了{}个连接，{}个IP", connectionDataList.size(), ipSet.size());
            
        } catch (Exception e) {
            log.error("远控木马告警分析失败", e);
        }
    }
    
    /**
     * 处理单个连接数据
     */
    private void processConnectionData(Map<String, Object> dataMap, 
                                     GraphBuilder builder, 
                                     List<AlarmJudgeVertexEntity> routeVertexList,
                                     Set<String> ipSet) {
        String sip = (String) dataMap.get("sIp");
        String dip = (String) dataMap.get("dIp");
        String key = sip + "_" + dip;
        
        ipSet.add(sip);
        ipSet.add(dip);
        
        // 添加连接边
        builder.addConnectionEdge(sip, dip, (String) dataMap.get("AppName"));
        
        // 处理标签
        processLabels(dataMap, builder, key);
        
        // 处理SSL指纹连接
        processSslFingerConnections(builder, routeVertexList, sip, dip);
    }
    
    /**
     * 处理标签信息
     */
    private void processLabels(Map<String, Object> dataMap, GraphBuilder builder, String key) {
        List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new java.util.ArrayList<>());
        if (CollectionUtils.isNotEmpty(labelList)) {
            List<AnalysisLabelInfoEntity> labelsInfo = queryService.findLabelInfoByIds(labelList);
            
            Map<String, Object> labelMap = new HashMap<>();
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            builder.addSessionLabel(labelMap);
            
            // 添加标签边
            builder.addLabelEdge((String) dataMap.get("sIp"), key + "_label", key);
            builder.addLabelEdge(key + "_label", (String) dataMap.get("dIp"), key);
        }
    }
    
    /**
     * 处理SSL指纹连接
     */
    private void processSslFingerConnections(GraphBuilder builder, 
                                           List<AlarmJudgeVertexEntity> routeVertexList,
                                           String sip, String dip) {
        if (CollectionUtils.isNotEmpty(routeVertexList)) {
            for (AlarmJudgeVertexEntity sslFingerVertex : routeVertexList) {
                // 源IP到SSL指纹的边
                builder.addLabelEdge(sip, sslFingerVertex.getVid(), StringUtils.EMPTY);
                // SSL指纹到目标IP的边
                builder.addLabelEdge(sslFingerVertex.getVid(), dip, StringUtils.EMPTY);
            }
        }
    }
}
