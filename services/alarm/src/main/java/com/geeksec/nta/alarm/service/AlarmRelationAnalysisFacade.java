package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;

import java.util.Map;

/**
 * 告警关系分析门面服务接口
 * 协调各个子服务，提供统一的告警关系分析能力
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmRelationAnalysisFacade {
    
    /**
     * 生成告警关系分析图谱
     * 根据告警信息分析攻击链路，构建节点和边的关系网络
     * 
     * @param alarmMap 告警信息映射
     * @return 包含节点(vertex)、边(edge)和标签(label_vertex)的图谱数据
     */
    Map<String, Object> createAlarmRelationGraph(Map<String, Object> alarmMap);
    
    /**
     * 基于IP角色生成告警关系扩展分析图谱
     * 通过指定IP地址和角色信息，查询相关告警并进行关系扩展分析
     * 
     * @param condition 包含IP地址、角色、时间范围等条件的查询参数
     * @return 包含节点(vertex)、边(edge)和标签(label_vertex)的扩展图谱数据
     */
    Map<String, Object> createAlarmRelationGraphByRole(AlarmRoleJudgeCondition condition);
}
