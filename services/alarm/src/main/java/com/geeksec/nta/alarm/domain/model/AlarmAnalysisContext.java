package com.geeksec.nta.alarm.domain.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 告警分析上下文
 * 封装告警分析所需的所有上下文信息
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
public class AlarmAnalysisContext {
    
    /**
     * 原始告警数据
     */
    private Map<String, Object> alarmMap;
    
    /**
     * 告警知识库ID
     */
    private Integer alarmKnowledgeId;
    
    /**
     * 告警类型
     */
    private String alarmType;
    
    /**
     * 告警会话列表
     */
    private List<String> sessionList;
    
    /**
     * 攻击者IP列表
     */
    private List<String> attackerIpList;
    
    /**
     * 受害者IP列表
     */
    private List<String> victimIpList;
    
    /**
     * 从告警Map创建分析上下文
     */
    public static AlarmAnalysisContext fromAlarmMap(Map<String, Object> alarmMap) {
        AlarmAnalysisContextBuilder builder = AlarmAnalysisContext.builder()
                .alarmMap(alarmMap);
        
        // 提取告警知识库ID
        Object alarmKnowledgeId = alarmMap.get("alarm_knowledge_id");
        if (alarmKnowledgeId instanceof String) {
            builder.alarmKnowledgeId(Integer.parseInt((String) alarmKnowledgeId));
        } else if (alarmKnowledgeId instanceof Number) {
            builder.alarmKnowledgeId(((Number) alarmKnowledgeId).intValue());
        }
        
        // 提取告警类型
        Object alarmType = alarmMap.get("alarm_type");
        if (alarmType instanceof String) {
            builder.alarmType((String) alarmType);
        }
        
        // 提取会话列表
        Object sessionListObj = alarmMap.get("alarm_session_list");
        if (sessionListObj instanceof List) {
            builder.sessionList((List<String>) sessionListObj);
        }
        
        return builder.build();
    }
    
    /**
     * 是否有告警类型
     */
    public boolean hasAlarmType() {
        return alarmType != null && !alarmType.trim().isEmpty();
    }
    
    /**
     * 是否有会话列表
     */
    public boolean hasSessionList() {
        return sessionList != null && !sessionList.isEmpty();
    }
    
    /**
     * 获取告警知识库ID，如果为null则返回0
     */
    public Integer getAlarmKnowledgeIdOrDefault() {
        return alarmKnowledgeId != null ? alarmKnowledgeId : 0;
    }
}
