package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;

import java.util.List;
import java.util.Map;

/**
 * 告警图谱数据提取服务接口
 * 负责从告警原始数据中提取图谱构建所需的结构化信息
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmGraphDataExtractor {
    
    /**
     * 提取IP列表（按角色）
     * 
     * @param alarmMap 告警数据
     * @param role 角色类型（attacker、victim等）
     * @return IP地址列表
     */
    List<String> extractIpListByRole(Map<String, Object> alarmMap, String role);
    
    /**
     * 提取攻击路径信息
     * 
     * @param alarmMap 告警数据
     * @return 攻击路径节点列表
     */
    List<AlarmJudgeVertexEntity> extractAttackRoutes(Map<String, Object> alarmMap);
    
    /**
     * 提取连接数据列表
     * 
     * @param alarmMap 告警数据
     * @param alarmKnowledgeId 告警知识库ID
     * @return 连接数据列表
     */
    List<Map<String, Object>> extractConnectionDataList(Map<String, Object> alarmMap, Integer alarmKnowledgeId);
    
    /**
     * 提取目标信息列表
     * 
     * @param alarmMap 告警数据
     * @return 目标信息列表
     */
    List<Map<String, Object>> extractTargets(Map<String, Object> alarmMap);
}
