package com.geeksec.nta.alarm.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.service.core.AlarmAnalyticsService;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 告警统计分析控制器
 * 负责告警的统计和聚合分析
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@RequestMapping("/alarm/analytics")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "告警统计分析", description = "告警数据的统计和聚合分析")
public class AlarmAnalyticsController {
    
    private final AlarmAnalyticsService alarmAnalyticsService;
    
    /**
     * 获取告警指标统计
     */
    @PostMapping("/target-agg")
    @Operation(summary = "获取告警指标统计", description = "获取各级别告警数量、攻击者数量、受害者数量等统计信息")
    public ApiResponse<AlarmTargetAggVo> getAlarmTargetAgg(@RequestBody AlarmCommonCondition condition) {
        log.info("获取告警指标统计请求: {}", condition);
        
        try {
            AlarmTargetAggVo result = alarmAnalyticsService.getAlarmTargetAgg(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取告警指标统计失败", e);
            return ApiResponse.error("获取告警指标统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取攻击链路统计
     */
    @PostMapping("/attack-chain")
    @Operation(summary = "获取攻击链路统计", description = "获取告警攻击链路的聚合统计信息")
    public ApiResponse<List<AlarmTypeAggVo.AttackChain>> getAttackChainAggr(@RequestBody AlarmCommonCondition condition) {
        log.info("获取攻击链路统计请求: {}", condition);
        
        try {
            List<AlarmTypeAggVo.AttackChain> result = alarmAnalyticsService.getAttackChainAggr(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取攻击链路统计失败", e);
            return ApiResponse.error("获取攻击链路统计失败: " + e.getMessage());
        }
    }
    
    // TODO: 后续可扩展更多分析功能
    
    /**
     * 获取告警趋势分析
     */
    /*
    @PostMapping("/trend")
    @Operation(summary = "获取告警趋势分析", description = "按时间维度统计告警数量变化趋势")
    public ApiResponse<List<AlarmTrendVo>> getAlarmTrend(@RequestBody AlarmCommonCondition condition) {
        log.info("获取告警趋势分析请求: {}", condition);
        
        try {
            List<AlarmTrendVo> result = alarmAnalyticsService.getAlarmTrend(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取告警趋势分析失败", e);
            return ApiResponse.error("获取告警趋势分析失败: " + e.getMessage());
        }
    }
    */
    
    /**
     * 获取告警热点分析
     */
    /*
    @PostMapping("/hotspot")
    @Operation(summary = "获取告警热点分析", description = "统计最频繁的攻击者、受害者、攻击类型等")
    public ApiResponse<AlarmHotspotVo> getAlarmHotspot(@RequestBody AlarmCommonCondition condition) {
        log.info("获取告警热点分析请求: {}", condition);
        
        try {
            AlarmHotspotVo result = alarmAnalyticsService.getAlarmHotspot(condition);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("获取告警热点分析失败", e);
            return ApiResponse.error("获取告警热点分析失败: " + e.getMessage());
        }
    }
    */
}
