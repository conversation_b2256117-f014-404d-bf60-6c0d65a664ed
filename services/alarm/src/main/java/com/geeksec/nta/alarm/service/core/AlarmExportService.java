package com.geeksec.nta.alarm.service.core;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;

import java.util.List;

/**
 * 告警导出服务接口
 * 负责各种告警数据的导出功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmExportService {
    
    /**
     * 导出告警报告PDF
     * 
     * @param condition 查询条件
     * @return PDF文件路径
     */
    String exportAlarmReport(AlarmListCondition condition);
    
    /**
     * 准备告警会话PCAP下载
     * 通过告警关联会话ID检索对应会话信息，生成PCAP下载列表
     * 
     * @param userId 用户ID
     * @param alarmSessionList 告警会话ID列表
     * @param alarmType 告警类型
     * @param alarmTime 告警时间
     * @return 准备结果
     */
    ApiResponse prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime);
    
    /**
     * 导出告警数据为CSV格式
     * 
     * @param condition 查询条件
     * @return CSV文件内容或文件路径
     */
    // TODO: 后续可以将CSV导出从Controller中提取到这里
    // String exportAlarmToCsv(AlarmListCondition condition);
    
    /**
     * 导出告警统计报表
     * 
     * @param condition 查询条件
     * @return 统计报表文件路径
     */
    // TODO: 后续可扩展更多导出功能
    // String exportAlarmStatistics(AlarmCommonCondition condition);
}
