package com.geeksec.nta.alarm.service.impl;

import com.geeksec.nta.alarm.domain.model.AlarmAnalysisContext;
import com.geeksec.nta.alarm.domain.model.GraphAnalysisResult;
import com.geeksec.nta.alarm.domain.model.GraphBuilder;
import com.geeksec.nta.alarm.entity.AlarmJudgeEdgeEntity;
import com.geeksec.nta.alarm.entity.AlarmJudgeVertexEntity;
import com.geeksec.nta.alarm.entity.AnalysisLabelInfoEntity;
import com.geeksec.nta.alarm.service.infrastructure.AlarmGraphDataExtractor;
import com.geeksec.nta.alarm.service.infrastructure.AlarmGraphQueryService;
import com.geeksec.nta.alarm.service.AlarmTypeAnalysisService;
import com.geeksec.nta.alarm.service.AlarmTypeHandler;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 告警类型分析服务实现类
 * 负责根据不同告警类型执行相应的分析逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmTypeAnalysisServiceImpl implements AlarmTypeAnalysisService {
    
    private final List<AlarmTypeHandler> handlers;
    private final AlarmGraphDataExtractor dataExtractor;
    private final AlarmGraphQueryService queryService;
    
    @Override
    public GraphAnalysisResult analyzeByAlarmType(AlarmAnalysisContext context) {
        log.debug("根据告警类型分析: alarmKnowledgeId={}", context.getAlarmKnowledgeId());
        
        GraphBuilder builder = new GraphBuilder();
        Integer alarmKnowledgeId = context.getAlarmKnowledgeIdOrDefault();
        
        // 查找支持的处理器
        AlarmTypeHandler handler = findHandler(alarmKnowledgeId);
        if (handler != null) {
            log.debug("使用处理器: {} 处理告警类型: {}", handler.getHandlerName(), alarmKnowledgeId);
            handler.analyze(context, builder);
        } else {
            log.warn("未找到支持告警类型 {} 的处理器", alarmKnowledgeId);
        }
        
        return builder.build();
    }
    
    @Override
    public GraphAnalysisResult analyzeBySessionId(AlarmAnalysisContext context) {
        log.debug("根据会话ID分析");
        
        List<Map<String, Object>> targets = dataExtractor.extractTargets(context.getAlarmMap());
        if (CollectionUtils.isEmpty(targets)) {
            log.debug("目标列表为空，返回空结果");
            return GraphAnalysisResult.empty();
        }
        
        String sessionId = (String) targets.get(0).get("name");
        if (StringUtils.isBlank(sessionId)) {
            log.debug("会话ID为空，返回空结果");
            return GraphAnalysisResult.empty();
        }
        
        Map<String, Object> dataMap = queryService.findConnectionLogBySessionId(sessionId);
        if (dataMap == null || dataMap.isEmpty()) {
            log.debug("未找到会话数据: sessionId={}", sessionId);
            return GraphAnalysisResult.empty();
        }
        
        return buildGraphFromConnectionData(dataMap);
    }
    
    /**
     * 查找支持指定告警类型的处理器
     */
    private AlarmTypeHandler findHandler(Integer alarmKnowledgeId) {
        return handlers.stream()
                .filter(handler -> handler.supports(alarmKnowledgeId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 从连接数据构建图谱
     */
    private GraphAnalysisResult buildGraphFromConnectionData(Map<String, Object> dataMap) {
        GraphBuilder builder = new GraphBuilder();
        Set<String> ipSet = new HashSet<>();
        
        String sIp = (String) dataMap.get("sIp");
        String dIp = (String) dataMap.get("dIp");
        String key = sIp + "_" + dIp;
        
        ipSet.add(sIp);
        ipSet.add(dIp);
        
        // 添加连接边
        builder.addConnectionEdge(sIp, dIp, (String) dataMap.get("AppName"));
        
        // 处理标签
        List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new java.util.ArrayList<>());
        if (CollectionUtils.isNotEmpty(labelList)) {
            List<AnalysisLabelInfoEntity> labelsInfo = queryService.findLabelInfoByIds(labelList);
            
            Map<String, Object> labelMap = new HashMap<>();
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            builder.addSessionLabel(labelMap);
            
            // 添加标签边
            builder.addLabelEdge(sIp, key + "_label", key);
            builder.addLabelEdge(key + "_label", dIp, key);
        }
        
        // 添加IP节点
        builder.addIpVerticesByRole(ipSet, null, null);
        
        return builder.build();
    }
}
