package com.geeksec.nta.alarm.service.infrastructure.impl;

import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import com.geeksec.nta.alarm.entity.AnalysisLabelInfoEntity;
import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.mapper.DorisConnectMapper;
import com.geeksec.nta.alarm.service.infrastructure.AlarmGraphQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 告警图谱数据查询服务实现类
 * 负责从各种数据源查询告警分析所需的原始数据
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmGraphQueryServiceImpl implements AlarmGraphQueryService {
    
    private final AlarmMapper alarmMapper;
    private final DorisConnectMapper dorisConnectMapper;
    // TODO: 待实现 ThAnalysisDao
    // private final ThAnalysisDao thAnalysisDao;
    
    @Override
    public List<Map<String, Object>> findAlarmsByRoleAndIp(String ipAddr, 
                                                          List<String> roles, 
                                                          AlarmRoleJudgeCondition.TimeRange timeRange, 
                                                          Integer limit) {
        log.debug("查询告警数据: ipAddr={}, roles={}, timeRange={}, limit={}", 
                 ipAddr, roles, timeRange, limit);
        
        try {
            return alarmMapper.findAlarmsByRoleAndIp(ipAddr, roles, timeRange, limit);
        } catch (Exception e) {
            log.error("查询告警数据失败: ipAddr={}, roles={}", ipAddr, roles, e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public List<Map<String, Object>> findConnectionLogsBySessionIds(List<String> sessionIds, int limit) {
        log.debug("查询连接日志: sessionIds={}, limit={}", sessionIds, limit);
        
        if (sessionIds == null || sessionIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            List<Map<String, Object>> results = dorisConnectMapper.findConnectLogsBySessionIds(sessionIds, limit);
            
            if (results == null || results.isEmpty()) {
                log.warn("在 connect_log 表中未找到会话 [{}], 尝试在 ssl_log 表中查找 (此逻辑待实现)", sessionIds);
                // TODO: 实现SSL日志查询
                // results = dorisConnectMapper.findSslLogsBySessionIds(sessionIds, limit);
            }
            
            return results != null ? results : Collections.emptyList();
        } catch (Exception e) {
            log.error("查询连接日志失败: sessionIds={}", sessionIds, e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public Map<String, Object> findConnectionLogBySessionId(String sessionId) {
        log.debug("查询单个连接日志: sessionId={}", sessionId);
        
        if (sessionId == null || sessionId.trim().isEmpty()) {
            return Collections.emptyMap();
        }
        
        try {
            return dorisConnectMapper.findConnectLogBySessionId(sessionId);
        } catch (Exception e) {
            log.error("查询单个连接日志失败: sessionId={}", sessionId, e);
            return Collections.emptyMap();
        }
    }
    
    @Override
    public List<AnalysisLabelInfoEntity> findLabelInfoByIds(List<Integer> labelIds) {
        log.debug("查询标签信息: labelIds={}", labelIds);
        
        if (labelIds == null || labelIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        try {
            // TODO: 待实现 ThAnalysisDao
            // return thAnalysisDao.getTagInfoByIds(labelIds);
            log.warn("ThAnalysisDao 尚未实现，返回空列表");
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("查询标签信息失败: labelIds={}", labelIds, e);
            return Collections.emptyList();
        }
    }
}
