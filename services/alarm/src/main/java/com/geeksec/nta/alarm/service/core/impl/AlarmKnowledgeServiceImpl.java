package com.geeksec.nta.alarm.service.core.impl;

import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.service.core.AlarmKnowledgeService;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 告警知识库服务实现类
 * 负责告警知识库相关操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmKnowledgeServiceImpl implements AlarmKnowledgeService, CommandLineRunner {
    
    private final AlarmMapper alarmMapper;
    
    @Override
    public void run(String... args) throws Exception {
        // 系统启动时自动初始化知识库
        log.info("系统启动，开始初始化告警知识库");
        initKnowledgeType();
    }
    
    @Override
    public void initKnowledgeType() {
        log.info("开始初始化告警知识库和采集规则字典");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 初始化告警知识库字典
            initAlarmKnowledgeDict();
            
            // 初始化采集规则字典
            initCollectionRuleDict();
            
            long endTime = System.currentTimeMillis();
            log.info("告警知识库初始化完成，耗时: {}ms", (endTime - startTime));
            
        } catch (Exception e) {
            log.error("初始化告警知识库失败", e);
            throw new RuntimeException("初始化告警知识库失败", e);
        }
    }
    
    @Override
    public List<KnowledgeAlarmVo> getKnowledgeAlarmList() {
        log.info("获取告警知识库全量列表");
        
        try {
            List<KnowledgeAlarmVo> knowledgeList = alarmMapper.getKnowledgeAlarmList();
            
            if (knowledgeList == null) {
                log.warn("告警知识库列表为空");
                return List.of();
            }
            
            log.info("获取到{}条告警知识库记录", knowledgeList.size());
            return knowledgeList;
            
        } catch (Exception e) {
            log.error("获取告警知识库列表失败", e);
            throw new RuntimeException("获取告警知识库列表失败", e);
        }
    }
    
    /**
     * 初始化告警知识库字典
     */
    private void initAlarmKnowledgeDict() {
        log.debug("初始化告警知识库字典");
        
        try {
            // 检查知识库表是否存在数据
            long knowledgeCount = alarmMapper.countKnowledgeAlarms();
            
            if (knowledgeCount == 0) {
                log.info("告警知识库为空，开始初始化默认数据");
                initDefaultKnowledgeData();
            } else {
                log.info("告警知识库已存在{}条记录，跳过初始化", knowledgeCount);
            }
            
            // 刷新知识库缓存（如果有缓存机制）
            refreshKnowledgeCache();
            
        } catch (Exception e) {
            log.error("初始化告警知识库字典失败", e);
            throw e;
        }
    }
    
    /**
     * 初始化采集规则字典
     */
    private void initCollectionRuleDict() {
        log.debug("初始化采集规则字典");
        
        try {
            // 检查采集规则表是否存在数据
            long ruleCount = alarmMapper.countCollectionRules();
            
            if (ruleCount == 0) {
                log.info("采集规则字典为空，开始初始化默认数据");
                initDefaultCollectionRules();
            } else {
                log.info("采集规则字典已存在{}条记录，跳过初始化", ruleCount);
            }
            
        } catch (Exception e) {
            log.error("初始化采集规则字典失败", e);
            throw e;
        }
    }
    
    /**
     * 初始化默认知识库数据
     */
    private void initDefaultKnowledgeData() {
        log.info("开始初始化默认告警知识库数据");
        
        try {
            // 这里应该插入默认的告警知识库数据
            // 例如：扫描行为、远控木马、挖矿病毒等常见告警类型
            
            // 示例数据（实际应该从配置文件或数据库脚本中读取）
            alarmMapper.insertDefaultKnowledgeData();
            
            log.info("默认告警知识库数据初始化完成");
            
        } catch (Exception e) {
            log.error("初始化默认知识库数据失败", e);
            throw e;
        }
    }
    
    /**
     * 初始化默认采集规则
     */
    private void initDefaultCollectionRules() {
        log.info("开始初始化默认采集规则数据");
        
        try {
            // 这里应该插入默认的采集规则数据
            alarmMapper.insertDefaultCollectionRules();
            
            log.info("默认采集规则数据初始化完成");
            
        } catch (Exception e) {
            log.error("初始化默认采集规则失败", e);
            throw e;
        }
    }
    
    /**
     * 刷新知识库缓存
     */
    private void refreshKnowledgeCache() {
        log.debug("刷新告警知识库缓存");
        
        try {
            // TODO: 如果使用了缓存（如Redis、Caffeine等），在这里刷新缓存
            // 例如：
            // cacheManager.evict("knowledgeCache");
            // 或者重新加载缓存数据
            
            log.debug("告警知识库缓存刷新完成");
            
        } catch (Exception e) {
            log.warn("刷新告警知识库缓存失败", e);
            // 缓存刷新失败不应该影响主流程，只记录警告日志
        }
    }
}
