package com.geeksec.nta.alarm.service.impl;

import com.geeksec.nta.alarm.domain.model.AlarmAnalysisContext;
import com.geeksec.nta.alarm.domain.model.GraphAnalysisResult;
import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import com.geeksec.nta.alarm.service.AlarmGraphBuilderService;
import com.geeksec.nta.alarm.service.AlarmGraphQueryService;
import com.geeksec.nta.alarm.service.AlarmRelationAnalysisFacade;
import com.geeksec.nta.alarm.service.AlarmTypeAnalysisService;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 告警关系分析门面服务实现类
 * 协调各个子服务，提供统一的告警关系分析能力
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmRelationAnalysisFacadeImpl implements AlarmRelationAnalysisFacade {
    
    private final AlarmGraphQueryService queryService;
    private final AlarmTypeAnalysisService typeAnalysisService;
    private final AlarmGraphBuilderService graphBuilderService;
    
    @Override
    public Map<String, Object> createAlarmRelationGraph(Map<String, Object> alarmMap) {
        log.info("开始创建告警关系分析图谱");
        
        try {
            // 1. 构建分析上下文
            AlarmAnalysisContext context = AlarmAnalysisContext.fromAlarmMap(alarmMap);
            log.debug("分析上下文构建完成: alarmKnowledgeId={}, hasAlarmType={}, hasSessionList={}", 
                     context.getAlarmKnowledgeId(), context.hasAlarmType(), context.hasSessionList());
            
            // 2. 根据告警类型进行分析
            GraphAnalysisResult analysisResult;
            if (context.hasAlarmType()) {
                log.debug("使用告警类型分析模式");
                analysisResult = typeAnalysisService.analyzeByAlarmType(context);
            } else {
                log.debug("使用会话ID分析模式");
                analysisResult = typeAnalysisService.analyzeBySessionId(context);
            }
            
            // 3. 构建图谱数据
            Map<String, Object> graphData = graphBuilderService.buildGraphData(analysisResult);
            
            // 4. 优化和去重
            Map<String, Object> optimizedGraphData = graphBuilderService.optimizeGraphData(graphData);
            
            log.info("告警关系分析图谱创建完成");
            return optimizedGraphData;
            
        } catch (Exception e) {
            log.error("创建告警关系分析图谱失败", e);
            return createEmptyGraphData();
        }
    }
    
    @Override
    public Map<String, Object> createAlarmRelationGraphByRole(AlarmRoleJudgeCondition condition) {
        log.info("开始基于IP角色创建告警关系扩展分析图谱: ipAddr={}, roles={}", 
                condition.getIpAddr(), condition.getRole());
        
        try {
            // 1. 查询告警数据
            List<Map<String, Object>> alarms = queryService.findAlarmsByRoleAndIp(
                    condition.getIpAddr(), 
                    condition.getRole(), 
                    condition.getTimeRange(), 
                    condition.getQueryNum());
            
            if (CollectionUtils.isEmpty(alarms)) {
                log.info("未找到符合条件的告警数据");
                return createEmptyGraphData();
            }
            
            log.debug("查询到{}条告警数据", alarms.size());
            
            // 2. 批量分析告警
            List<Map<String, Object>> graphDataList = new ArrayList<>();
            for (Map<String, Object> alarmMap : alarms) {
                try {
                    Map<String, Object> graphData = createAlarmRelationGraph(alarmMap);
                    if (graphData != null && !isEmptyGraphData(graphData)) {
                        graphDataList.add(graphData);
                    }
                } catch (Exception e) {
                    log.warn("分析单个告警失败，跳过: alarmId={}", alarmMap.get("id"), e);
                }
            }
            
            if (CollectionUtils.isEmpty(graphDataList)) {
                log.info("所有告警分析结果均为空");
                return createEmptyGraphData();
            }
            
            // 3. 合并结果
            Map<String, Object> mergedGraphData = graphBuilderService.mergeGraphData(graphDataList);
            
            log.info("基于IP角色的告警关系扩展分析图谱创建完成，合并了{}个图谱", graphDataList.size());
            return mergedGraphData;
            
        } catch (Exception e) {
            log.error("基于IP角色创建告警关系扩展分析图谱失败", e);
            return createEmptyGraphData();
        }
    }
    
    /**
     * 创建空图谱数据
     */
    private Map<String, Object> createEmptyGraphData() {
        return graphBuilderService.buildGraphData(GraphAnalysisResult.empty());
    }
    
    /**
     * 判断是否为空图谱数据
     */
    private boolean isEmptyGraphData(Map<String, Object> graphData) {
        if (graphData == null || graphData.isEmpty()) {
            return true;
        }
        
        List<?> vertices = (List<?>) graphData.get("vertex");
        List<?> edges = (List<?>) graphData.get("edge");
        List<?> labels = (List<?>) graphData.get("label_vertex");
        
        return (vertices == null || vertices.isEmpty()) &&
               (edges == null || edges.isEmpty()) &&
               (labels == null || labels.isEmpty());
    }
}
